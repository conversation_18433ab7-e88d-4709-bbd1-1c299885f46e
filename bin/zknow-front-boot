#!/usr/bin/env node

const program = require('commander');
const package = require('../package.json');

program
  .version(package.version)
  .usage('[command] [options]')
  .command('start [options]', 'to start a server')
  .command('dist [options]', 'to build micro server or a single frontend bundle')
  .command('compile [option]', 'to compile dir')
  .command('prepublish [option]', 'to prepublish')
  .parse(process.argv);

process.on('SIGINT', function() {
  program.runningCommand && program.runningCommand.kill('SIGKILL');
  process.exit(0);
});
