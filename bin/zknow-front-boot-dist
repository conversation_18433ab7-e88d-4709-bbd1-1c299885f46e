#!/usr/bin/env node

const program = require('commander');
const build = require('../lib/bin/build').default;

program
  .option('-c, --config <path>', 'set config path. defaults to ./choerodon.config.js')
  .option('-e, --env <path>', 'NODE_ENV in build')
  .option('-s, --src <path>', 'source')
  .option('-l, --lib <path>', 'library')
  .option('-x,  --external', 'for external')
  .parse(process.argv);

build(program);
