const path = require('path');
const gulp = require('gulp');
const rimraf = require('rimraf');
const babel = require('gulp-babel');
const through2 = require('through2');
const merge2 = require('merge2');
const getBabelCommonConfig = require('./src/bin/common/config/getBabelCommonConfig');

const libDir = path.resolve('lib');
const exclude = /(routes|entry)\.nunjucks\.(js|jsx)/;

function babelify(js, dir = '') {
  const babelConfig = getBabelCommonConfig({});
  const stream = js.pipe(babel(babelConfig));
  return stream
    .pipe(through2.obj(function (file, encoding, next) {
      const matches = file.path.match(exclude);
      if (matches) {
        const content = file.contents.toString(encoding);
        file.contents = Buffer.from(content
          .replace(`'{{ ${matches[1]} }}'`, `{{ ${matches[1]} }}`)
          .replace('\'{{ home }}\'', '{{ home }}')
          .replace('\'{{ master }}\'', '{{ master }}')
          );
      }
      this.push(file);
      next();
    }))
    .pipe(gulp.dest(path.join(libDir, dir)));
}

function compileAssets() {
  return gulp.src([
    'src/**/*.@(jpg|png|svg|scss|less|html|ico|gif|css|json)',
    '!src/main/**/*.@(jpg|png|svg|scss|less|html|ico|gif|css|json)',
  ]).pipe(gulp.dest(libDir));
}

function compileFile() {
  return babelify(gulp.src([
    'src/**/*.js?(x)',
    'src/**/*.ts?(x)',
    '!src/main/**/*.js?(x)',
  ]));
}

function compile() {
  rimraf.sync(libDir);
  return merge2([
    compileAssets(),
    compileFile(),
  ]);
}
function copyTo(dir) {
  rimraf.sync(dir);
  return gulp.src('lib/**/*')
    .pipe(gulp.dest(dir));
}
function copy(done) {
  const dirs = [
    '/Users/<USER>/dwps/low-code-service/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/low-code-helper/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/base-service-pro/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/workflow-service-x/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/knowledgebase-service/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/choerodon-front-master/node_modules/@buildrun/boot/lib',
    '/Users/<USER>/dwps/devops-service/node_modules/@buildrun/boot/lib'
  ];
  dirs.forEach(function(dir) {
    rimraf.sync(dir);
    gulp.src('lib/**/*')
      .pipe(gulp.dest(dir));
  });
  done();
}

gulp.task('compile', (done) => {
  compile()
    .on('finish', done);
});

gulp.task('start', (done) => {
  const start = require('./lib/bin/start').default;
  start({}, done);
});

gulp.task('copy', gulp.series(copy));
