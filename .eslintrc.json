{"parser": "babel-es<PERSON>", "env": {"browser": true, "node": true, "jasmine": true, "jest": true, "es6": true}, "settings": {"react": {"version": "16.9"}}, "plugins": ["babel", "react", "react-hooks", "markdown", "jest", "@typescript-eslint", "no-chinese"], "extends": ["airbnb", "prettier", "plugin:jest/recommended", "plugin:react/recommended", "plugin:import/typescript", "prettier/react"], "overrides": [{"files": ["*.ts", "*.tsx"], "rules": {"@typescript-eslint/no-unused-vars": [2, {"args": "none"}], "no-unused-vars": [2, {"argsIgnorePattern": "^_"}]}}], "rules": {"class-methods-use-this": "off", "consistent-return": "off", "global-require": "off", "import/extensions": "off", "import/no-duplicates": "off", "import/no-dynamic-require": "off", "import/no-extraneous-dependencies": "off", "import/no-named-as-default": "off", "import/no-named-as-default-member": "off", "import/no-unresolved": "off", "jsx-a11y/anchor-has-content": "off", "jsx-a11y/anchor-is-valid": ["warn", {"aspects": ["<PERSON><PERSON><PERSON><PERSON>"]}], "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/href-no-hash": "off", "jsx-a11y/interactive-supports-focus": "off", "jsx-a11y/label-has-associated-control": "off", "jsx-a11y/label-has-for": "off", "jsx-a11y/no-noninteractive-element-interactions": "off", "jsx-a11y/no-static-element-interactions": "off", "linebreak-style": "off", "max-len": "off", "no-chinese/no-chinese": "error", "no-console": [2, {"allow": ["warn", "error"]}], "no-control-regex": "off", "no-debugger": "off", "no-else-return": "off", "no-nested-ternary": "off", "no-param-reassign": "off", "no-trailing-spaces": "off", "no-unused-vars": "off", "object-curly-newline": "off", "react/destructuring-assignment": "off", "react/display-name": "off", "react/forbid-prop-types": "off", "react/jsx-filename-extension": "off", "react/jsx-no-bind": ["error", {"ignoreRefs": true, "allowArrowFunctions": true, "allowBind": true}], "react/jsx-one-expression-per-line": "off", "react/jsx-props-no-spreading": "off", "react/no-unused-prop-types": "off", "react/prefer-stateless-function": ["off", {"ignorePureComponents": true}], "react/prop-types": "off", "react/require-default-props": "off", "react/sort-comp": "off", "react/state-in-constructor": "off"}, "globals": {"ZKnow": true}}