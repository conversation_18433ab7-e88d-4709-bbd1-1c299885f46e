/**
 * Module Federation 2.0 共享组件渲染示例
 * 展示各种渲染共享组件的方法
 */

import React, { useState } from 'react';
import { 
  RemoteComponent, 
  LazyRemoteComponent, 
  RemoteComponentChecker,
  RemoteComponentBundle 
} from '../src/bin/common/utils/RemoteComponentRenderer';

// 示例 1: 基础远程组件渲染
const BasicRemoteComponentExample = () => {
  return (
    <div>
      <h3>基础远程组件</h3>
      <RemoteComponent 
        remoteName="shared-components" 
        modulePath="./Button"
        type="primary"
        onClick={() => alert('远程按钮被点击!')}
      >
        点击我
      </RemoteComponent>
    </div>
  );
};

// 示例 2: 带错误处理的远程组件
const ErrorHandlingExample = () => {
  return (
    <div>
      <h3>带错误处理的远程组件</h3>
      <RemoteComponent 
        remoteName="shared-components" 
        modulePath="./NonExistentComponent"
        fallback={<div>🔄 加载中...</div>}
        errorFallback={(error) => (
          <div style={{ 
            padding: '12px', 
            backgroundColor: '#fff1f0', 
            border: '1px solid #ffccc7',
            borderRadius: '4px',
            color: '#ff4d4f'
          }}>
            ❌ 组件加载失败: {error.message}
          </div>
        )}
        onError={(error) => console.log('组件加载错误:', error)}
      />
    </div>
  );
};

// 示例 3: 使用 React.lazy 的远程组件
const LazyRemoteExample = () => {
  return (
    <div>
      <h3>Lazy 远程组件</h3>
      <LazyRemoteComponent 
        remoteName="shared-components" 
        modulePath="./Card"
        title="远程卡片"
        content="这是一个从远程加载的卡片组件"
        fallback={<div>🔄 懒加载中...</div>}
      />
    </div>
  );
};

// 示例 4: 远程服务可用性检查
const AvailabilityCheckExample = () => {
  return (
    <div>
      <h3>远程服务可用性检查</h3>
      <RemoteComponentChecker 
        remoteName="shared-components"
        fallback={<div>⚠️ 远程服务暂时不可用</div>}
      >
        <RemoteComponent 
          remoteName="shared-components" 
          modulePath="./Table"
          dataSource={[
            { key: '1', name: '张三', age: 32 },
            { key: '2', name: '李四', age: 28 },
          ]}
          columns={[
            { title: '姓名', dataIndex: 'name', key: 'name' },
            { title: '年龄', dataIndex: 'age', key: 'age' },
          ]}
        />
      </RemoteComponentChecker>
    </div>
  );
};

// 示例 5: 批量加载远程组件
const BatchLoadExample = () => {
  const [componentBundle, setComponentBundle] = useState(null);

  const componentsToLoad = {
    Button: { remoteName: 'shared-components', modulePath: './Button' },
    Input: { remoteName: 'shared-components', modulePath: './Input' },
    Modal: { remoteName: 'shared-components', modulePath: './Modal' },
  };

  return (
    <div>
      <h3>批量加载远程组件</h3>
      <RemoteComponentBundle 
        components={componentsToLoad}
        fallback={<div>🔄 批量加载组件中...</div>}
        onAllLoaded={(components) => {
          console.log('所有组件加载完成:', components);
          setComponentBundle(components);
        }}
      />
      
      {componentBundle && (
        <div style={{ marginTop: '16px' }}>
          <h4>加载完成的组件:</h4>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {Object.keys(componentBundle.components).map(key => (
              <div key={key} style={{ 
                padding: '8px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '4px' 
              }}>
                {key} ✅
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 示例 6: 本地共享组件使用
const LocalSharedComponentExample = () => {
  return (
    <div>
      <h3>本地共享组件</h3>
      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
        {/* 这些组件通过 shared 配置共享 */}
        <div>
          <h4>ZKnow 组件库</h4>
          {/* 
          import { Button, Input, Table } from '@zknow/components';
          <Button type="primary">ZKnow 按钮</Button>
          <Input placeholder="ZKnow 输入框" />
          */}
          <div style={{ padding: '8px', backgroundColor: '#f0f0f0' }}>
            @zknow/components 组件示例
          </div>
        </div>
        
        <div>
          <h4>Choerodon UI</h4>
          {/* 
          import { Button, DatePicker } from 'choerodon-ui';
          <Button type="primary">Choerodon 按钮</Button>
          <DatePicker placeholder="选择日期" />
          */}
          <div style={{ padding: '8px', backgroundColor: '#f0f0f0' }}>
            choerodon-ui 组件示例
          </div>
        </div>
      </div>
    </div>
  );
};

// 主示例组件
const RemoteComponentUsageExamples = () => {
  const [activeTab, setActiveTab] = useState('basic');

  const tabs = [
    { key: 'basic', label: '基础用法', component: BasicRemoteComponentExample },
    { key: 'error', label: '错误处理', component: ErrorHandlingExample },
    { key: 'lazy', label: 'Lazy 加载', component: LazyRemoteExample },
    { key: 'check', label: '可用性检查', component: AvailabilityCheckExample },
    { key: 'batch', label: '批量加载', component: BatchLoadExample },
    { key: 'local', label: '本地共享', component: LocalSharedComponentExample },
  ];

  const ActiveComponent = tabs.find(tab => tab.key === activeTab)?.component;

  return (
    <div style={{ padding: '24px' }}>
      <h2>Module Federation 2.0 共享组件渲染示例</h2>
      
      {/* 标签页 */}
      <div style={{ 
        display: 'flex', 
        gap: '8px', 
        marginBottom: '24px',
        borderBottom: '1px solid #d9d9d9',
        paddingBottom: '8px'
      }}>
        {tabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            style={{
              padding: '8px 16px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              backgroundColor: activeTab === tab.key ? '#1890ff' : '#fff',
              color: activeTab === tab.key ? '#fff' : '#000',
              cursor: 'pointer',
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* 内容区域 */}
      <div style={{ 
        padding: '24px', 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        backgroundColor: '#fafafa'
      }}>
        {ActiveComponent && <ActiveComponent />}
      </div>
    </div>
  );
};

export default RemoteComponentUsageExamples;
