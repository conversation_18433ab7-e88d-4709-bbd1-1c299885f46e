/**
 * ExternalComponent 迁移示例
 * 展示如何从旧版本迁移到 MF 2.0 版本
 */

import React, { useState } from 'react';
import ExternalComponent from '../src/external-component';           // 原版本（已升级）
import ExternalComponentV2 from '../src/external-component-v2';     // MF 2.0 专用版本

// 示例 1: 原有用法（保持兼容）
const OriginalUsageExample = () => {
  const [loading, setLoading] = useState(false);

  return (
    <div>
      <h3>原有用法（向后兼容）</h3>
      <div style={{ marginBottom: '16px' }}>
        加载状态: {loading ? '加载中...' : '已完成'}
      </div>
      
      <ExternalComponent
        system={{
          scope: 'lcr',
          module: 'PageLoader',
        }}
        fallback={<div>🔄 加载中...</div>}
        setLoad={setLoading}
        viewId="example-view"
        mode="READONLY"
      />
    </div>
  );
};

// 示例 2: 升级后的用法（启用 MF 2.0）
const UpgradedUsageExample = () => {
  const [loading, setLoading] = useState(false);

  return (
    <div>
      <h3>升级后的用法（MF 2.0 增强）</h3>
      <div style={{ marginBottom: '16px' }}>
        加载状态: {loading ? '加载中...' : '已完成'}
      </div>
      
      <ExternalComponent
        system={{
          scope: 'lcr',
          module: 'PageLoader',
        }}
        useMF2={true}                    // 启用 MF 2.0
        fallback={<div>🔄 MF 2.0 加载中...</div>}
        setLoad={setLoading}
        onLoad={(component) => console.log('组件加载成功:', component)}
        onError={(error) => console.error('组件加载失败:', error)}
        retryCount={3}                   // 自动重试 3 次
        timeout={15000}                  // 15秒超时
        viewId="example-view"
        mode="READONLY"
      />
    </div>
  );
};

// 示例 3: 使用 MF 2.0 专用版本
const MF2SpecializedExample = () => {
  return (
    <div>
      <h3>MF 2.0 专用版本</h3>
      
      <ExternalComponentV2
        remoteName="lcr"
        modulePath="./PageLoader"
        fallback={<div>🚀 MF 2.0 专用加载中...</div>}
        enableCache={true}               // 启用缓存
        checkAvailability={true}         // 检查服务可用性
        retryCount={3}                   // 重试次数
        timeout={20000}                  // 超时时间
        onLoad={(component) => {
          console.log('MF 2.0 组件加载成功:', component);
        }}
        onError={(error) => {
          console.error('MF 2.0 组件加载失败:', error);
          // 可以发送错误日志到监控系统
        }}
        // 传递给组件的 props
        viewId="example-view"
        mode="READONLY"
        defaultData={{}}
      />
    </div>
  );
};

// 示例 4: 错误处理和重试
const ErrorHandlingExample = () => {
  const CustomErrorComponent = ({ error, retry }) => (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #ff4d4f', 
      borderRadius: '8px',
      backgroundColor: '#fff2f0',
      textAlign: 'center'
    }}>
      <h4 style={{ color: '#ff4d4f', margin: '0 0 12px 0' }}>
        🚨 组件加载失败
      </h4>
      <p style={{ margin: '0 0 12px 0', fontSize: '14px' }}>
        错误信息: {error.message}
      </p>
      <button 
        onClick={retry}
        style={{
          padding: '8px 16px',
          border: 'none',
          borderRadius: '4px',
          backgroundColor: '#ff4d4f',
          color: 'white',
          cursor: 'pointer',
        }}
      >
        重新加载
      </button>
    </div>
  );

  return (
    <div>
      <h3>自定义错误处理</h3>
      
      <ExternalComponentV2
        remoteName="non-existent-app"
        modulePath="./NonExistentComponent"
        ErrorComponent={CustomErrorComponent}
        retryCount={2}
        onError={(error) => {
          // 发送错误日志
          console.error('组件加载错误:', {
            remoteName: 'non-existent-app',
            modulePath: './NonExistentComponent',
            error: error.message,
            timestamp: new Date().toISOString(),
          });
        }}
      />
    </div>
  );
};

// 示例 5: 批量预加载
const PreloadExample = () => {
  const [preloadStatus, setPreloadStatus] = useState({});

  const preloadComponents = async () => {
    const componentsToPreload = [
      { remoteName: 'lcr', modulePath: './PageLoader' },
      { remoteName: 'shared-components', modulePath: './Button' },
      { remoteName: 'shared-components', modulePath: './Table' },
    ];

    const results = {};
    
    for (const { remoteName, modulePath } of componentsToPreload) {
      try {
        await ExternalComponentV2.preload(remoteName, modulePath);
        results[`${remoteName}${modulePath}`] = '✅ 成功';
      } catch (error) {
        results[`${remoteName}${modulePath}`] = `❌ 失败: ${error.message}`;
      }
    }
    
    setPreloadStatus(results);
  };

  return (
    <div>
      <h3>组件预加载</h3>
      
      <button 
        onClick={preloadComponents}
        style={{
          padding: '8px 16px',
          border: '1px solid #1890ff',
          borderRadius: '4px',
          backgroundColor: '#1890ff',
          color: 'white',
          cursor: 'pointer',
          marginBottom: '16px',
        }}
      >
        预加载组件
      </button>
      
      {Object.keys(preloadStatus).length > 0 && (
        <div>
          <h4>预加载结果:</h4>
          {Object.entries(preloadStatus).map(([key, status]) => (
            <div key={key} style={{ marginBottom: '4px' }}>
              <code>{key}</code>: {status}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// 示例 6: 性能监控
const PerformanceMonitoringExample = () => {
  const [loadTimes, setLoadTimes] = useState([]);

  const handleLoad = (component) => {
    const loadTime = performance.now();
    setLoadTimes(prev => [...prev, {
      component: component.name || 'Unknown',
      time: loadTime,
      timestamp: new Date().toLocaleTimeString(),
    }]);
  };

  return (
    <div>
      <h3>性能监控</h3>
      
      <ExternalComponentV2
        remoteName="lcr"
        modulePath="./PageLoader"
        onLoad={handleLoad}
        viewId="performance-test"
        mode="READONLY"
      />
      
      {loadTimes.length > 0 && (
        <div style={{ marginTop: '16px' }}>
          <h4>加载时间记录:</h4>
          {loadTimes.map((record, index) => (
            <div key={index} style={{ fontSize: '12px', marginBottom: '4px' }}>
              {record.timestamp}: {record.component} - {record.time.toFixed(2)}ms
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// 主示例组件
const ExternalComponentMigrationExamples = () => {
  const [activeTab, setActiveTab] = useState('original');

  const tabs = [
    { key: 'original', label: '原有用法', component: OriginalUsageExample },
    { key: 'upgraded', label: '升级用法', component: UpgradedUsageExample },
    { key: 'mf2', label: 'MF 2.0 专用', component: MF2SpecializedExample },
    { key: 'error', label: '错误处理', component: ErrorHandlingExample },
    { key: 'preload', label: '预加载', component: PreloadExample },
    { key: 'performance', label: '性能监控', component: PerformanceMonitoringExample },
  ];

  const ActiveComponent = tabs.find(tab => tab.key === activeTab)?.component;

  return (
    <div style={{ padding: '24px' }}>
      <h2>ExternalComponent 迁移指南</h2>
      
      {/* 标签页 */}
      <div style={{ 
        display: 'flex', 
        gap: '8px', 
        marginBottom: '24px',
        borderBottom: '1px solid #d9d9d9',
        paddingBottom: '8px'
      }}>
        {tabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            style={{
              padding: '8px 16px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              backgroundColor: activeTab === tab.key ? '#1890ff' : '#fff',
              color: activeTab === tab.key ? '#fff' : '#000',
              cursor: 'pointer',
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* 内容区域 */}
      <div style={{ 
        padding: '24px', 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        backgroundColor: '#fafafa'
      }}>
        {ActiveComponent && <ActiveComponent />}
      </div>
      
      {/* 清理缓存按钮 */}
      <div style={{ marginTop: '16px', textAlign: 'center' }}>
        <button 
          onClick={() => {
            ExternalComponentV2.clearCache();
            alert('缓存已清理');
          }}
          style={{
            padding: '8px 16px',
            border: '1px solid #faad14',
            borderRadius: '4px',
            backgroundColor: '#faad14',
            color: 'white',
            cursor: 'pointer',
          }}
        >
          清理组件缓存
        </button>
      </div>
    </div>
  );
};

export default ExternalComponentMigrationExamples;
