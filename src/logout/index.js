import { getCookieToken, removeAccessToken } from '../access-token';
import { ACCESS_TOKEN, AUTH_HOST } from '../constants';

export default function logout() {
  const token = getCookieToken();
  const redirectPath = window.location.hash.slice(1).includes('unauthorized') ? localStorage.getItem('historyPath') || '/' : window.location.hash.slice(1);
  // 为了把这个hash传到oauth里要把#换成%23
  const uri = encodeURIComponent(`${window.location.origin}/#${redirectPath}`);
  window.localStorage.removeItem('lastClosedId');
  // 移除redirect_uri(会影响单点退出)
  let logoutUrl = `${AUTH_HOST}/logout`;

  if (token) {
    logoutUrl += `?${ACCESS_TOKEN}=${getCookieToken()}`;
  }
  removeAccessToken();
  sessionStorage.clear();
  window.location = logoutUrl;
}
