import { Route, Switch, withRouter } from 'react-router-dom';

import { Store } from '@zknow/utils';

export { asyncRouter, asyncLocaleProvider } from '@zknow/utils';

export { StatusTag, LoadingBar, Loading } from '@zknow/components';

[
  'Choerodon',
  'MasterHeader',
  'CommonMenu',
  'Page',
  'Content',
  'Header',
  'Action',
  'Permission',
  'Remove',
  'axios',
  'store',
  'stores',
  'nomatch',
  'noaccess',
  'WSHandler',
  'PageTab',
  'PageWrap',
  'TabPage',
  'Breadcrumb',
  ['Route', () => Route],
  ['Switch', () => Switch],
  ['withRouter', () => withRouter],
  ['Master', (map) => map.default],
].forEach((key) => {
  let defaults = null;
  if (Array.isArray(key)) {
    [key, defaults] = key;
  }

  Object.defineProperty(exports, key, {
    enumerable: true,
    get() {
      const masterMap = Store.get(Store.MASTER);
      const value = masterMap[key];
      if (value === undefined && defaults) {
        return defaults(masterMap);
      }
      return value;
    },
  });
});
