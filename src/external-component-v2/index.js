/**
 * ExternalComponent V2 - Module Federation 2.0 增强版
 * 专为 MF 2.0 设计的远程组件渲染器
 */

import React, { useState, useEffect, useCallback } from 'react';
import loadRemoteModule from '../load-remote-module';
import checkRemoteAvailability from '../check-remote-availability';
import Loading from '../loading';

const componentCache = new Map();
const availabilityCache = new Map();

/**
 * MF 2.0 增强版外部组件
 */
function ExternalComponentV2(props) {
  const {
    // 基础配置
    system,                    // { scope, module } 或 { remoteName, modulePath }
    remoteName,               // 远程应用名称（优先级高于 system.scope）
    modulePath,               // 模块路径（优先级高于 system.module）

    // 显示配置
    fallback = <Loading />,   // 加载中显示
    notFound,                 // 未找到时显示
    ErrorComponent,           // 错误组件

    // 回调函数
    onLoad,                   // 加载成功回调
    onError,                  // 错误回调
    setLoad,                  // 加载状态回调（兼容旧版）

    // 高级配置
    enableCache = true,       // 是否启用缓存
    checkAvailability = false, // 是否检查可用性
    retryCount = 2,           // 重试次数
    timeout = 30000,          // 超时时间

    // 其他 props 传递给组件
    ...componentProps
  } = props;

  // 状态管理
  const [Component, setComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [isAvailable, setIsAvailable] = useState(null);

  // 解析配置
  const finalRemoteName = remoteName || system?.scope;
  const finalModulePath = modulePath || (system?.module ? `./${system.module}` : './');

  // 缓存键
  const cacheKey = `${finalRemoteName}${finalModulePath}`;

  // 检查可用性
  useEffect(() => {
    if (!checkAvailability || !finalRemoteName) return;

    const checkCacheKey = finalRemoteName;
    const cachedAvailability = availabilityCache.get(checkCacheKey);

    if (cachedAvailability !== undefined) {
      setIsAvailable(cachedAvailability);
      return;
    }

    checkRemoteAvailability(finalRemoteName).then(available => {
      availabilityCache.set(checkCacheKey, available);
      setIsAvailable(available);
    });
  }, [finalRemoteName, checkAvailability]);

  // 加载组件
  const loadComponent = useCallback(async () => {
    if (!finalRemoteName || !finalModulePath) {
      setError(new Error('缺少必要的配置参数'));
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (setLoad) setLoad(false);

      // 检查缓存
      if (enableCache && componentCache.has(cacheKey)) {
        const cachedComponent = componentCache.get(cacheKey);
        setComponent(() => cachedComponent);
        setLoading(false);
        if (setLoad) setLoad(true);
        if (onLoad) onLoad(cachedComponent);
        return;
      }

      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('组件加载超时')), timeout);
      });

      // 加载模块
      const loadPromise = loadRemoteModule(finalRemoteName, finalModulePath);
      const module = await Promise.race([loadPromise, timeoutPromise]);

      const LoadedComponent = module.default || module;

      // 缓存组件
      if (enableCache) {
        componentCache.set(cacheKey, LoadedComponent);
      }

      setComponent(() => LoadedComponent);
      setLoading(false);
      setRetryAttempts(0);

      if (setLoad) setLoad(true);
      if (onLoad) onLoad(LoadedComponent);

    } catch (err) {
      console.error(`[ExternalComponentV2] 加载失败: ${finalRemoteName}${finalModulePath}`, err);
      setError(err);
      setLoading(false);

      if (setLoad) setLoad(true);
      if (onError) onError(err);

      // 自动重试
      if (retryAttempts < retryCount) {
        setTimeout(() => {
          setRetryAttempts(prev => prev + 1);
          loadComponent();
        }, 2000 * (retryAttempts + 1)); // 递增延迟
      }
    }
  }, [
    finalRemoteName,
    finalModulePath,
    cacheKey,
    enableCache,
    timeout,
    retryAttempts,
    retryCount,
    setLoad,
    onLoad,
    onError
  ]);

  // 初始加载
  useEffect(() => {
    if (checkAvailability && isAvailable === false) {
      return; // 服务不可用，不加载
    }

    if (checkAvailability && isAvailable === null) {
      return; // 还在检查可用性
    }

    loadComponent();
  }, [loadComponent, isAvailable]);

  // 重试函数
  const retry = useCallback(() => {
    setRetryAttempts(0);
    setError(null);
    loadComponent();
  }, [loadComponent]);

  // 渲染逻辑
  if (!finalRemoteName) {
    return <h2>Not system specified</h2>;
  }

  if (checkAvailability && isAvailable === null) {
    return fallback;
  }

  if (checkAvailability && isAvailable === false) {
    return notFound || (
      <div style={{
        padding: '16px',
        border: '1px solid #faad14',
        borderRadius: '6px',
        backgroundColor: '#fffbe6',
        color: '#faad14',
        textAlign: 'center',
      }}>
        ⚠️ 远程服务 {finalRemoteName} 暂时不可用
      </div>
    );
  }

  if (loading) {
    return fallback;
  }

  if (error) {
    if (ErrorComponent) {
      return <ErrorComponent error={error} retry={retry} />;
    }

    return (
      <div style={{
        padding: '16px',
        border: '1px solid #ff4d4f',
        borderRadius: '6px',
        backgroundColor: '#fff2f0',
        color: '#ff4d4f',
        textAlign: 'center',
      }}>
        <div>⚠️ 组件加载失败</div>
        <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
          {finalRemoteName}{finalModulePath}
        </div>
        <div style={{ fontSize: '12px', marginTop: '4px' }}>
          {error.message}
        </div>
        {retryAttempts < retryCount && (
          <div style={{ fontSize: '12px', marginTop: '4px' }}>
            重试中... ({retryAttempts + 1}/{retryCount + 1})
          </div>
        )}
        <button
          onClick={retry}
          style={{
            marginTop: '8px',
            padding: '4px 8px',
            border: '1px solid #ff4d4f',
            borderRadius: '4px',
            backgroundColor: 'transparent',
            color: '#ff4d4f',
            cursor: 'pointer',
          }}
        >
          手动重试
        </button>
      </div>
    );
  }

  if (!Component) {
    return notFound || <div>组件不存在</div>;
  }

  return (
    <React.Suspense fallback={fallback}>
      <Component {...componentProps} />
    </React.Suspense>
  );
}

// 清理缓存的工具函数
ExternalComponentV2.clearCache = () => {
  componentCache.clear();
  availabilityCache.clear();
};

// 预加载组件
ExternalComponentV2.preload = async (remoteName, modulePath) => {
  const cacheKey = `${remoteName}${modulePath}`;
  if (componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey);
  }

  try {
    const module = await loadRemoteModule(remoteName, modulePath);
    const Component = module.default || module;
    componentCache.set(cacheKey, Component);
    return Component;
  } catch (error) {
    console.error(`[ExternalComponentV2] 预加载失败: ${remoteName}${modulePath}`, error);
    throw error;
  }
};

export default ExternalComponentV2;
