import { ACCESS_TOKEN, COOKIE_SERVER, TOKEN_TYPE } from '../constants';
import { getCookie, removeCookie, setCookie } from '../cookie';
import getEnv from '../get-env';

let cachedToken = null;

const cookieReg = /(?:^https?:\/\/)|(?:^\.)/;

export function getCookieServer() {
  const searchParams = new URLSearchParams(window.location.hash?.split('?')[1]);
  if (searchParams.get('hostCookieServer')) {
    return window.location.host;
  }
  return COOKIE_SERVER;
}

export function getCookieToken() {
  const option = {
    path: '/',
  };
  const token = getCookie(ACCESS_TOKEN, option);
  if (token && cachedToken && token !== cachedToken) {
    return null;
  }
  return token;
}

/**
 * 前端存储cookie token
 */
export function setAccessToken(token, tokenType, expiresIn) {
  const option = {
    path: '/',
    sameSite: getEnv('COOKIES_OPTION_SAME_SITE') || 'none',
    secure: !getEnv('COOKIES_OPTION_SECURE')
      ? true
      : getEnv('COOKIES_OPTION_SECURE') !== 'false',
    httpOnly: false,
  };
  if (expiresIn) {
    const expires = expiresIn * 1000;
    option.expires = new Date(Date.now() + expires);
  }
  if (getCookieServer()) {
    const cookieServer = getCookieServer().replace(cookieReg, '');
    if (window.location.href.indexOf(cookieServer) === -1) {
      setCookie(ACCESS_TOKEN, token, option);
      setCookie(TOKEN_TYPE, tokenType, option);
    }
    option.domain = cookieServer;
  }
  setCookie(ACCESS_TOKEN, token, option);
  setCookie(TOKEN_TYPE, tokenType, option);
  cachedToken = token;
}

/**
 * 获取cookie token
 */
export function getAccessToken() {
  const option = {
    path: '/',
  };
  const accessToken = getCookieToken();
  const tokenType = getCookie(TOKEN_TYPE, option);
  if (accessToken && tokenType) {
    return `${tokenType} ${accessToken}`;
  }
  return null;
}

/**
 * 移除token
 */
export function removeAccessToken() {
  const option = {
    path: '/',
    domain: window.location.hostname,
  };
  const cookieServer = getCookieServer()?.replace(cookieReg, '');
  if (cookieServer) {
    if (window.location.href.indexOf(cookieServer) === -1) {
      removeCookie(ACCESS_TOKEN, option);
      removeCookie(TOKEN_TYPE, option);
    }
    // 移除当前二级域下所有子域的token
    const cookieServerArray = cookieServer.split('.');
    while (cookieServerArray.length >= 2) {
      option.domain = cookieServerArray.join('.');
      removeCookie(ACCESS_TOKEN, option);
      removeCookie(TOKEN_TYPE, option);
      cookieServerArray.shift();
    }

    // 移除带.的
    const fullDomain = window.location.hostname; // 当前页面的完整域名
    option.domain = `.${fullDomain}`;
    removeCookie(ACCESS_TOKEN, option);
    removeCookie(TOKEN_TYPE, option);
    //
    option.domain = cookieServer;
  }
  removeCookie(ACCESS_TOKEN, option);
  removeCookie(TOKEN_TYPE, option);
}
