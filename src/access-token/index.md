---
title: asyncLocaleProvider
nav:
  title: utils
---

# asyncLocaleProvider

多语言 intlProvider 的封装，用于注册 locale 。

```jsx | pure
import React from 'react';
import { inject } from 'mobx-react';
import { asyncLocaleProvider } from '@zknow/utils';

function ASSETIndex({ AppState: { currentLanguage: language } }) {
  const IntlProviderAsync = asyncLocaleProvider(language, () => import(`./locale/${language}`));
  return (
    <IntlProviderAsync>
      <div className="c7ncd-root">
        
      </div>
    </IntlProviderAsync>
  );
}

export default inject('AppState')(ASSETIndex);

```

