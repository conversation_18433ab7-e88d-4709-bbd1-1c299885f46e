import urljoin from 'url-join';
import getModuleEntryDependencies from './getModuleEntryDependencies';

export default function getChunkDependencies(basePath, nameSpace, module, dependencyPaths = []) {
  const dependencies = getModuleEntryDependencies(nameSpace, module);
  if (dependencies) {
    Object.keys(dependencies).forEach((id) => {
      if (!__webpack_modules__[id]) {
        const sourceFiles = dependencies[id];
        sourceFiles.forEach((chunkFile) => {
          const path = urljoin(basePath, chunkFile);
          if (dependencyPaths.indexOf(path) === -1) {
            dependencyPaths.push(path);
            dependencyPaths = getChunkDependencies(basePath, nameSpace, chunkFile, dependencyPaths);
          }
        });
      }
    });
  }
  return dependencyPaths;
}
