import fs from 'fs';
import moment from 'moment';
import context from './common/context';


export default function prePublish(program) {
  const { initContext } = context;
  initContext(program, 'build');

  const { packagePath } = context;
  const pack: any = fs.readFileSync(packagePath);
  const packageJSON = JSON.parse(pack);
  packageJSON.version += `-${  process.env.CI_COMMIT_REF_NAME}.${moment().format('YYYYMMDDHHmmss')}`;
  // 同步写入package.json文件
  fs.writeFileSync(packagePath, JSON.stringify(packageJSON,null,2));
}
