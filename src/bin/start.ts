// import path from 'path';
import webpack from "webpack";
import WebpackDevServer from "webpack-dev-server";
import openBrowser from "react-dev-utils/openBrowser";
import { choosePort, prepareUrls } from "react-dev-utils/WebpackDevServerUtils";
import chalk from "chalk";
import context from "./common/context";
// import generateTransfer from './common/generateTransfer';
import generateEntry from "./common/generateEntry";
import generateWebpackConfig from "./common/generateWebpackConfig";
// import generateEnvironmentVariable from './common/generateEnvironmentVariable';

export default function start(program, done) {
  // 初始化全局参数context
  const { initContext } = context;
  program.env = "development";
  initContext(program, "start", !!done);

  const {
    projectConfig: { entryName, devServerConfig, port },
  } = context;
  // generateTransfer(entryName);
  // 生成入口文件
  generateEntry(entryName);

  const HOST = process.env.HOST || "0.0.0.0";
  const DEFAULT_PORT = parseInt(port, 10) || 3000;

  choosePort(HOST, DEFAULT_PORT).then((PORT) => {
    if (PORT === null) {
      // We have not found a port.
      return;
    }
    const protocol = process.env.HTTPS === "true" ? "https" : "http";
    const urls = prepareUrls(protocol, HOST, PORT, "/");

    const webpackConfig = generateWebpackConfig();
    const serverOptions = {
      client: {
        overlay: false,
      },
      hot: true,
      ...devServerConfig,
      historyApiFallback: true,
      host: "localhost",
      port,
    };
    const compiler = webpack(webpackConfig);

    const devServer = new WebpackDevServer(compiler, serverOptions);
    // devServer.listen(port, "0.0.0.0", () => {
    //   openBrowser(`http://localhost:${port}`);
    //   if (done) {
    //     done();
    //   }
    // });

    devServer.startCallback(() => {
      console.log(chalk.cyan("Starting the development server...\n"));
      openBrowser(urls.localUrlForBrowser);
    });

    ["SIGINT", "SIGTERM"].forEach(function (sig) {
      process.on(sig, function () {
        devServer.close();
        process.exit();
      });
    });
  });
}
