import path from 'path';
import gulp from 'gulp';
import rimraf from 'rimraf';
import babel from 'gulp-babel';
import merge2 from 'merge2';
import aliases from 'gulp-style-aliases';
import getBabelCommonConfig from './common/config/getBabelCommonConfig';
import context from './common/context';
import { style as alias } from './common/config/getAlias';


function compileStyles() {
  const { src, lib } = context;
  return gulp.src([
    path.resolve(`${src}/**/*.@(css|scss|less)`),
  ])
    .pipe(aliases(alias))
    .pipe(gulp.dest(path.resolve(lib)));
}

function compileAssets() {
  const { src, lib } = context;
  return gulp.src([
    path.resolve(`${src}/**/*.@(jpg|jpeg|png|ico|gif|svg|html|json|ttf|woff|eot|md|txt)`),
  ]).pipe(gulp.dest(path.resolve(lib)));
}

function babelify(js) {
  const { projectConfig: { babelConfig, browsers }, mode, env, lib } = context;

  const config = babelConfig(getBabelCommonConfig({ mode, browsers }), mode, env);
  return js.pipe(babel(config)).pipe(gulp.dest(path.resolve(lib)));
}

function compileFile() {
  const source = [
    path.resolve(`${context.src}/**/*.js?(x)`),
    path.resolve(`${context.src}/**/*.ts?(x)`),
  ];
  return babelify(gulp.src(source));
}

export default function compile(program) {
  // 初始化全局参数context
  const { initContext } = context;
  initContext(program, 'compile');
  const libDir = path.resolve(context.lib);
  rimraf.sync(libDir);
  return merge2([
    compileAssets(),
    compileStyles(),
    compileFile(),
  ]);
}
