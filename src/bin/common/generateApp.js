import fs from 'fs';
import path from 'path';
import nunjucks from 'nunjucks';
import context from './context';
import escapeWinPath from './utils/escapeWinPath';
import generateRoute from './generateRoute';

export default function generateApp(configEntryName) {
  const { tmpDirPath, projectConfig: { routeType } } = context;

  const appPath = path.join(tmpDirPath, `app.${configEntryName}.js`);

  const appTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/app.nunjucks.js')).toString();

  const routesPath = escapeWinPath(generateRoute(configEntryName));
  const content = nunjucks.renderString(appTemplate, {
    routeType,
    routesPath,
  });
  fs.writeFileSync(
    appPath,
    content,
  );
  return appPath;
}
