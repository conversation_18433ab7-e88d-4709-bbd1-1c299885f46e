import context from '../context';
import getPackageInfo from '../utils/getPackageInfo';
import getPackageRoute from '../utils/getPackageRoute';

export default function handleCollectRoute() {
  const { projectConfig, lib, src, mode, projectConfig: { modules, routes } } = context;
  projectConfig.routes = modules.reduce((obj, module) => {
    const packageInfo = getPackageInfo(module);
    return Object.assign(obj, getPackageRoute(packageInfo, module, mode, lib, src));
  }, routes || {});
}
