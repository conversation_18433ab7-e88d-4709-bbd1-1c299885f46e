import path from 'path';
import transformMain from './transformMain';
import getRouteName from './getRouteName';
import getRouteIndex from './getRouteIndex';

export default function getPackageRoute(packageInfo, base = '.', mode, lib, src, type) {
  if (packageInfo) {
    return {
      [getRouteName(packageInfo, type)]: base.startsWith('.')
        ? path.resolve(base, transformMain(getRouteIndex(packageInfo, type), lib, src))
        : require.resolve(base),
    };
  }
}
