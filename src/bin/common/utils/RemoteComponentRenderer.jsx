/**
 * Module Federation 2.0 远程组件渲染器
 * 提供统一的远程组件加载和渲染功能
 */

import React, { Suspense, Component, useState, useEffect } from 'react';
import loadRemoteModule from '../../../load-remote-module';
import checkRemoteAvailability from '../../../check-remote-availability';

/**
 * 错误边界组件
 */
class RemoteComponentErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('远程组件渲染失败:', error, errorInfo);
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error);
      }

      return (
        <div style={{
          padding: '16px',
          border: '1px solid #ff4d4f',
          borderRadius: '6px',
          backgroundColor: '#fff2f0',
          color: '#ff4d4f',
          textAlign: 'center',
        }}>
          <div style={{ marginBottom: '8px' }}>⚠️ 组件加载失败</div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>
            {this.props.remoteName}/{this.props.modulePath}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 远程组件加载器
 */
export const RemoteComponent = ({
  remoteName,
  modulePath = './',
  fallback = <div>加载中...</div>,
  errorFallback,
  onError,
  onLoad,
  ...props
}) => {
  const [RemoteComponentClass, setRemoteComponentClass] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let mounted = true;

    const loadComponent = async () => {
      try {
        setLoading(true);
        setError(null);

        const module = await loadRemoteModule(remoteName, modulePath);

        if (mounted) {
          // 支持默认导出和命名导出
          const Component = module.default || module;
          setRemoteComponentClass(() => Component);
          setLoading(false);

          if (onLoad) {
            onLoad(Component);
          }
        }
      } catch (err) {
        if (mounted) {
          setError(err);
          setLoading(false);
          console.error(`加载远程组件失败: ${remoteName}${modulePath}`, err);
        }
      }
    };

    loadComponent();

    return () => {
      mounted = false;
    };
  }, [remoteName, modulePath, onLoad]);

  if (loading) {
    return fallback;
  }

  if (error) {
    if (errorFallback) {
      return errorFallback(error);
    }
    return (
      <div style={{
        padding: '16px',
        border: '1px solid #ff4d4f',
        borderRadius: '6px',
        backgroundColor: '#fff2f0',
        color: '#ff4d4f',
        textAlign: 'center',
      }}>
        组件加载失败: {remoteName}{modulePath}
      </div>
    );
  }

  if (!RemoteComponentClass) {
    return <div>组件不存在</div>;
  }

  return (
    <RemoteComponentErrorBoundary
      remoteName={remoteName}
      modulePath={modulePath}
      fallback={errorFallback}
      onError={onError}
    >
      <RemoteComponentClass {...props} />
    </RemoteComponentErrorBoundary>
  );
};

/**
 * 使用 React.lazy 的远程组件
 */
export const LazyRemoteComponent = ({
  remoteName,
  modulePath = './',
  fallback = <div>加载中...</div>,
  errorFallback,
  onError,
  ...props
}) => {
  const RemoteComponentLazy = React.lazy(() =>
    loadRemoteModule(remoteName, modulePath)
  );

  return (
    <RemoteComponentErrorBoundary
      remoteName={remoteName}
      modulePath={modulePath}
      fallback={errorFallback}
      onError={onError}
    >
      <Suspense fallback={fallback}>
        <RemoteComponentLazy {...props} />
      </Suspense>
    </RemoteComponentErrorBoundary>
  );
};

/**
 * 远程组件可用性检查器
 */
export const RemoteComponentChecker = ({
  remoteName,
  children,
  fallback = <div>远程服务不可用</div>
}) => {
  const [isAvailable, setIsAvailable] = useState(null);

  useEffect(() => {
    checkRemoteAvailability(remoteName).then(setIsAvailable);
  }, [remoteName]);

  if (isAvailable === null) {
    return <div>检查远程服务中...</div>;
  }

  if (!isAvailable) {
    return fallback;
  }

  return children;
};

/**
 * 批量远程组件加载器
 */
export const RemoteComponentBundle = ({
  components,
  fallback = <div>加载组件包中...</div>,
  onAllLoaded
}) => {
  const [loadedComponents, setLoadedComponents] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadAllComponents = async () => {
      const loaded = {};

      for (const [key, { remoteName, modulePath }] of Object.entries(components)) {
        try {
          const module = await loadRemoteModule(remoteName, modulePath);
          loaded[key] = module.default || module;
        } catch (error) {
          console.error(`加载组件 ${key} 失败:`, error);
          loaded[key] = () => <div>组件 {key} 加载失败</div>;
        }
      }

      setLoadedComponents(loaded);
      setLoading(false);

      if (onAllLoaded) {
        onAllLoaded(loaded);
      }
    };

    loadAllComponents();
  }, [components, onAllLoaded]);

  if (loading) {
    return fallback;
  }

  return { components: loadedComponents };
};

export default {
  RemoteComponent,
  LazyRemoteComponent,
  RemoteComponentChecker,
  RemoteComponentBundle,
};
