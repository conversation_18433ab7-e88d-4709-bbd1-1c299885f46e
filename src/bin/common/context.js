import path from 'path';
import mkdirp from 'mkdirp';
import warning from './utils/warning';
import getProjectConfig from './config/getProjectConfig';
import getPackageInfo from './utils/getPackageInfo';

let isInitialized = false;

const defaultOptions = {
  src: 'react',
  lib: 'lib',
  env: process.env.NODE_ENV || 'production',
  config: 'project.config.js',
  packagePath: './package.json',
  dotenv: './react/.env',
  external: false,
  routeMap: {},
  exposes: {},
};

function initialize(context) {
  if (isInitialized) {
    warning(false, '`context` had been initialized');
    return;
  }
  const tmpDirPath = path.join(__dirname, '../../../tmp');
  context.tmpDirPath = tmpDirPath;
  mkdirp.sync(tmpDirPath);
  Object.assign(exports, context);
  isInitialized = true;
}

function initContext(options, mode, dev) {
  const opts = {
    ...defaultOptions,
    ...options,
  };
  const { config } = opts;
  const configFile = path.resolve(config);
  const packageInfo = getPackageInfo();
  const projectConfig = getProjectConfig(configFile);
  initialize({
    ...opts,
    projectConfig,
    packageInfo,
    mode,
    isDev: dev,
  });
};

exports.initContext = initContext;
