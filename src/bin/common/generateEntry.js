import fs from 'fs';
import path from 'path';
import nunjucks from 'nunjucks';
import context from './context';
import escapeWinPath from './utils/escapeWinPath';
import handleCollectRoute from './entry/handleCollectRoute';
import transformMain from './utils/transformMain';
import getRouteIndex from './utils/getRouteIndex';
import generateApp from './generateApp';

export default function generateEntry(configEntryName) {
  const { tmpDirPath, isDev, external, mode, packageInfo, lib, src, projectConfig: { master, projectType } } = context;

  // 生成主入口文件
  const entryPath = path.join(tmpDirPath, `entry.${configEntryName}.js`);
  const bootstrapPath = path.join(tmpDirPath, `bootstrap.${configEntryName}.js`);

  if (external && mode !== 'start') {
    const entryTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/external.nunjucks.js')).toString();

    fs.writeFileSync(
      entryPath,
      nunjucks.renderString(entryTemplate, {
        main: escapeWinPath(require.resolve(transformMain(path.resolve(getRouteIndex(packageInfo)), lib, src))),
      }),
    );
  } else {
    // 收集路由，单模块启动也得配置路径
    handleCollectRoute();
    // 生成路由文件
    const appPath = escapeWinPath(generateApp(configEntryName));
    const entryTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/entry.nunjucks.js')).toString();
    nunjucks.configure({ autoescape: false });
    fs.writeFileSync(
      entryPath,
      nunjucks.renderString(entryTemplate, {
        appPath,
        master: escapeWinPath(master.startsWith('.') ? path.resolve(master) : master),
      }),
    );
  }
  const bootstrapTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/bootstrap.nunjucks.js')).toString();
  fs.writeFileSync(
    bootstrapPath,
    nunjucks.renderString(bootstrapTemplate, {
      bootstrap: `entry.${configEntryName}.js`,
    }),
  );
}
