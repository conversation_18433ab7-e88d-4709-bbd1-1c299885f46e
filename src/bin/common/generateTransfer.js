import fs from 'fs';
import path from 'path';
import nunjucks from 'nunjucks';
import context from './context';

export default function generateTransfer(configEntryName) {
  const { tmpDirPath, projectConfig: { master } } = context;

  const transferPath = path.join(tmpDirPath, `transfer.${configEntryName}.js`);

  const transferTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/transfer.nunjucks.js')).toString();
  const masterLibrary = require(master.startsWith('.') ? path.resolve(master) : require.resolve(master));

  const exportPath = master.replace('master.js', 'index.js');
  // const content = astFunction(exportPath);
  const content = nunjucks.renderString(transferTemplate, {
    keys: Object.keys(masterLibrary).join(','),
  });
  fs.writeFileSync(
    transferPath,
    content,
  );
}
