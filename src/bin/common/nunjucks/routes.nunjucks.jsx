import React, { Suspense } from 'react';
import { ExternalRoute, Loading } from '@zknow/components';
import { nomatch, Route, Switch } from '../lib';

const routes = {};

function createRoute(path, component, moduleCode) {
  if (!routes[path]) {
    routes[path] = <Route path={path} component={React.lazy(component)} />;
  }
  return routes[path];
}

function createHome(path, component, homePath) {
  if (!routes[path]) {
    const Cmp = React.lazy(
      // eslint-disable-next-line no-nested-ternary
      homePath
        ? component
        : () => import('../{{ source }}/containers/home'),
    );
    routes[path] = <Route exact path={path} component={Cmp} />;
  }
  return routes[path];
}

const AutoRouter = () => (
  <Suspense fallback={<span className="dots-loader" />}>
    <Switch>
      {'{{ home }}'}
      {'{{ routes }}'}
      <Route path="*">
        <ExternalRoute NotFound={nomatch} />
      </Route>
    </Switch>
  </Suspense>
);

export default AutoRouter;
