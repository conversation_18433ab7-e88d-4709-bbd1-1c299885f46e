import React, { useEffect, useState } from 'react';
import { render } from 'react-dom';
import { initMaster, initMFRuntime } from '@zknow/utils';
import App from '{{ appPath }}';
import * as Master from '{{ master }}';

if (typeof process === 'undefined' && typeof window !== 'undefined' && !window.import) {
  window.import = require('dimport/legacy');
}
if (typeof process === 'undefined') {
  window.process = { env: {} };
}

// 初始化 Module Federation 2.0 运行时
initMFRuntime({
  debug: process.env.NODE_ENV === 'development',
});

const Index = () => {
  const [ready, setReady] = useState(false);
  useEffect(() => {
    initMaster(Master);
    setReady(true);
  }, []);
  if (ready) {
    return (
      <App />
    );
  }

  return <span className="dots-loader" />;
};

render(<Index />, document.getElementById('zknow-app'));
