import React, { Suspense } from 'react';
import { HashRouter as Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';
import { Modal } from 'choerodon-ui/pro';
import { Master, Route, Switch } from '../lib';

if (typeof process === 'undefined' && typeof window !== 'undefined' && !window.import) {
  window.import = require('dimport/legacy');
}
if (typeof process === 'undefined') {
  window.process = { env: {} };
}

const AutoRouter = React.lazy(() => import('{{ routesPath }}'));

const getConfirmation = (message, callback) => {
  const [title, content, funcKey] = message.split('__@.@__');
  Modal.confirm({
    className: 'c7n-iam-confirm-modal',
    title: content && title,
    children: content || title,
    onOk: async () => {
      if (window[funcKey]) {
        await window[funcKey](true)
        window[funcKey] = null;
      }
      callback(true);
    },
    onCancel: async () => {
      if (window[funcKey]) {
        await window[funcKey](false)
        window[funcKey] = null;
      }
      callback(false);
    },
  });
};

const App = () => (
  <Router hashHistory={createBrowserHistory} getUserConfirmation={getConfirmation}>
    <Suspense fallback={<span className="dots-loader" />}>
      <Switch>
        <Route path="/">
          {Master ? <Master AutoRouter={AutoRouter} /> : <AutoRouter />}
        </Route>
      </Switch>
    </Suspense>
  </Router>
);

export default App;
