import fs from 'fs';
import path from 'path';
import nunjucks from 'nunjucks';
import context from './context';
import escapeWinPath from './utils/escapeWinPath';

const routesTemplate = fs.readFileSync(path.join(__dirname, './nunjucks/routes.nunjucks.js')).toString();

export default function generateRoute(configEntryName) {
  const { tmpDirPath, isDev, projectConfig: { routes, homePath } } = context;
  const configRoutes = routes;
  const routesPath = path.join(tmpDirPath, `routes.${configEntryName}.js`);
  nunjucks.configure(routesPath, {
    autoescape: false,
  });
  const homePathStr = `createHome("/", ${homePath ? `function() { return import("${escapeWinPath(path.resolve(homePath || ''))}"); }` : null}, ${homePath ? `'${homePath}'` : homePath})`;

  fs.writeFileSync(
    routesPath,
    nunjucks.renderString(routesTemplate, {
      routes: Object.keys(configRoutes).map((key) => (
        `createRoute("/${key}", function() { return import("${escapeWinPath(path.resolve(configRoutes[key]))}"); }, "${key}")`
      )).join(',\n') || 'null',
      home: homePathStr,
      source: isDev ? 'src' : 'lib',
      name: configEntryName,
    }),
  );
  return routesPath;
}
