const runtimeVersion = require("@babel/runtime/package.json").version;

const alias = require("./getAlias").babel;
const babelImports = require("./getBabelImports");

module.exports = function babel({ modules = "auto", mode, browsers }) {
  const plugins = [
    [require.resolve("@babel/plugin-transform-typescript"), { isTSX: true }],
    require.resolve("@babel/plugin-transform-object-assign"),
    [
      require.resolve("@babel/plugin-transform-runtime"),
      { version: runtimeVersion },
    ],
    [
      require.resolve("@babel/plugin-transform-template-literals"),
      { loose: true },
    ],
    require.resolve("@babel/plugin-proposal-do-expressions"),
    require.resolve("@babel/plugin-proposal-export-default-from"),
    require.resolve("@babel/plugin-proposal-export-namespace-from"),
    require.resolve("@babel/plugin-proposal-optional-chaining"),
    require.resolve("@babel/plugin-syntax-dynamic-import"),
    [require.resolve("@babel/plugin-proposal-decorators"), { legacy: true }],
    require.resolve("@umijs/babel-plugin-auto-css-modules"),
    require.resolve("@babel/plugin-proposal-class-properties"),
    require.resolve("babel-plugin-lodash"),
    ...Object.keys(babelImports).map((key) => [
      require.resolve("babel-plugin-import"),
      {
        libraryName: key,
        libraryDirectory: "lib",
        style: false,
        // customName: (name) => {
        //   if (key === "choerodon-ui/pro" && name === 'data-set') {
        //     return 'choerodon-ui/dataset/data-set/index.js'; // 找到导出 DataSet 的文件
        //
        //   }
        //   if(key === "choerodon-ui" && name === 'data-set' ){
        //     return 'choerodon-ui/dataset/data-set/index.js';
        //   }
        //   // console.log('`${key}/lib/${name}`', `${key}/lib/${name}`)
        //   return `${key}/lib/${name}`;
        // },
      },
      babelImports[key][0],
    ]),
  ];

  if (mode === "compile") {
    plugins.push([
      require.resolve("babel-plugin-module-resolver"),
      {
        alias,
      },
    ]);
  }

  return {
    presets: [
      require.resolve("@babel/preset-react"),
      [
        require.resolve("@babel/preset-env"),
        {
          targets: {
            firefox: "52.9",
            browsers,
          },
          useBuiltIns: "entry",
          corejs: 3, // 如果你使用 core-js 3
          modules,
          exclude: ["@babel/plugin-proposal-dynamic-import"],
        },
      ],
    ],
    plugins,
  };
};
