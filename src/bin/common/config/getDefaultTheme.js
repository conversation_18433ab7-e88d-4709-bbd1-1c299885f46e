export default function defaultTheme(env) {
  return {
    // common
    'primary-color': '#2979FF',
    'minor-color': '#e9f1ff', // primary-color 10%
    'yq-table-hover-bg': '#f8faff', // primary-color 3%
    'yq-table-current-bg': '#e9f1ff', // primary-color 10%
    'border-radius-base': '0.04rem',
    'border-radius-sm': '0.02rem',
    'text-color': '#12274D',
    'heading-color': '#12274D',
    'border-color-base': 'fade(#CBD2DC, 50%)',
    'border-color-split': 'fade(#CBD2DC, 50%)',
    'disabled-bg': 'fade(#F2F3F5 , 60)',
    'error-bg-color': '#fff',
    'info-color': '#2979FF',
    'warning-color': '#FD7D23',
    'success-color': '#1AB335',
    'error-color': '#F34C4B',
    'font-size-base': '0.14rem',
    'item-hover-bg': '#F2F3F5',
    'item-active-bg': '#e9f1ff', // primary-color 10%
    'item-focus-bg': '#e9f1ff', // primary-color 10%
    // tag
    'tag-default-bg': '#F2F3F5', // input-tag-default-bg
    'tag-default-color': '@text-color', // input-tag-default-color
    'disabled-color': 'fade(@text-color, 45%)', // tabs disabled
    'tag-margin': '0 0.04rem 0 0',
    // Modal
    'modal-header-padding': '0.15rem 0.2rem',
    'modal-title-color': '@text-color',
    'modal-title-font-size-base': '0.16rem',
    'modal-title-line-height': '@modal-title-font-size-base + 0.08rem',
    'modal-body-padding': '0.2rem',
    'modal-footer-padding': '0.16rem',
    'modal-confirm-title-font-size-base': '0.16rem',
    'modal-confirm-title-line-height': '@modal-confirm-title-font-size-base + 0.08rem',
    // button
    'btn-height-sm': '0.28rem',
    'btn-height-base': '0.32rem',
    'btn-height-lg': '0.36rem',
    'btn-box-shadow': 'none',
    'btn-focus-box-shadow': 'none',
    'btn-active-box-shadow': 'none',
    'btn-font-weight': 500,
    'btn-padding-vertical-base': '0.04rem',
    'btn-line-height': '@font-size-base + 0.08rem',
    'btn-raised-border-width-base': '0.01rem',
    'btn-icon-size': '0.16rem',
    'btn-icon-only-size': '0.16rem',
    'btn-group-spacing': '0.08rem',
    // input
    'input-color': '@text-color',
    'input-height-base': '0.32rem',
    'input-placeholder-color': 'fade(@text-color, 45%)',
    'input-placeholder-disabled-color': 'fade(@text-color, 25%)',
    'input-border-color': '#D7E2EC',
    'input-hover-border-color': 'fade(@text-color, 65%)',
    'input-focus-border-color': '@primary-color',
    'input-active-box-shadow': 'none',
    'input-required-bg': '#fff',
    'input-disabled-bg': 'fade(#F2F3F5, 40%)',
    'input-disabled-color': 'fade(@text-color, 85%)',
    'input-addon-bg': '#FAFCFF',
    'input-tag-border-radius': '0.02rem',
    'input-tag-line-height-base': '0.2rem',
    'input-tag-disabled-bg': '#fff',
    'input-tag-disabled-color': 'fade(@text-color, 85%)',
    'input-padding-vertical-base': '0.04rem',
    'input-padding-horizontal': '0.08rem',
    'input-tag-height-base': '0.2rem',
    'input-icon-width-base': '0.32rem',
    'input-multiple-padding': '0.04rem 0.08rem',
    'field-icon-help-color': 'fade(@text-color, 65%)',
    'input-disabled-icon-color': 'fade(@text-color, 45%)',
    'input-disabled-selection-color': '#fff',
    // dropdown
    'dropdown-menu-padding-vertical-base': '0.04rem',
    // radio
    'radio-size': '0.16rem',
    'radio-border-width': '0.02rem',
    'radio-border-color': 'fade(@text-color, 32%)',
    'radio-disabled-border-color': '#E5E6EB', // TODO 未在规范中
    'radio-disabled-bg': '#F2F3F5',
    'radio-disabled-color': '#F2F3F5',
    'radio-disabled-check-color': '#FFFFFF',
    'radio-disabled-check-bg': '@primary-color',
    'radio-disabled-check-border-color': '@primary-color',
    // checkbox
    'checkbox-border-color': 'fade(@text-color, 32%)',
    'checkbox-uncheck-hover-border-color': 'fade(@text-color, 32%)',
    'checkbox-disabled-bg': '#F2F3F5',
    'checkbox-disabled-border-color': '#E5E6EB',
    'checkbox-disabled-check-color': '#fff',
    'checkbox-disabled-check-bg': '@primary-color',
    'checkbox-disabled-check-border-color': '@primary-color',
    // switch
    'switch-height': '0.24rem',
    'switch-line-height': '0.24rem',
    'switch-min-width': '0.4rem',
    'switch-label-color': '#fff',
    'switch-bg': 'fade(@text-color, 24%)',
    'switch-button-size': '0.16rem',
    'switch-padding-horizontal': '0.04rem',
    'switch-checked-bg': '@primary-color',
    'switch-checked-button-bg': '#fff',
    'switch-disabled-opacity': '0.45',
    'switch-icon-font-size': '0.12rem',
    // icon
    'icon-font-size-base': '.16rem',
    'icon-font-size-sm': '.14rem',
    'icon-font-size-lg': '.18rem',
    // form
    'label-wrapper-padding': '0.06rem 0 0.04rem 0.16rem',
    // 'label-wrapper-padding-vertical': '0.02rem',
    // 'label-wrapper-padding-horizontal': '0.08rem',
    'form-item-wrapper-padding': '0.04rem 0.9rem 0.04rem 0.16rem',
    // 'form-item-wrapper-padding-vertical': '0.02rem',
    // 'form-item-wrapper-padding-horizontal': '0.08rem',
    'label-color': 'fade(@text-color, 65%)',
    // Tooltip
    'tooltip-bg': '#1E3866',
    'tooltip-max-width': '3.76rem',
    'tooltip-min-height': '@font-size-base + 0.08rem',
    'tooltip-arrow-width': '0.04rem',
    'tooltip-distance': '0.01rem',
    // table
    'table-header-bg': '#fff',
    'table-header-color': '@text-color',
    'table-header-font-weight': '500',
    'table-row-hover-bg': '@yq-table-current-bg',
    'table-current-row-bg': '@yq-table-current-bg',
    'table-even-row-bg': '@yq-table-hover-bg',
    'table-combo-filter-placeholder-color': 'fade(@text-color, 45%)',
    // tabs
    'tab-horizontal-margin': '0 0.16rem 0 0.16rem',
    'tab-normal-color': '@text-color',
    'tabs-card-head-background': '#F7F8FA',
    'tab-bar-margin': '0',
    // alert
    'alert-message-color': '@text-color',
    // avatar
    'avatar-border-radius': '0.06rem',
    // collapse
    'collapse-header-padding': '0.08rem 0.10rem',
    'collapse-header-bg': '#F8F9FB',
    // date-picker
    'date-picker-width': '2.68rem',
    // tree
    'tree-title-height': '36px',
    'tree-node-padding': '0',
    'tree-node-selected-bg': '#e9f1ff', // primary-color 10%
    // rate
    'rate-star-color': '#FFD600',
  };
}
