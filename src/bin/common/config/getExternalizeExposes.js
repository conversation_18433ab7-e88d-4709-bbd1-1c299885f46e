/* eslint-disable no-restricted-syntax */
import fs from 'fs-extra';
import path from 'path';
import { isSourceFile } from 'typescript';
import axios from 'axios';
import context from '../context';

const apiUrl = 'https://table.zknow.com/fusion/v1/datasheets/dst6zjjsfkkpDQuQub/records?viewId=viwFtYutzRiWx&fieldKey=name&pageSize=1000';
const apiToken = 'uskIhLjmvdg7CuGzkv8I7i1';

function fileDisplay(filePath) {
  const externalFiles = {};
  // 根据文件路径读取文件，返回文件列表
  const files = fs.readdirSync(filePath);
  for (const filename of files) {
    const filedir = path.join(filePath, filename);
    // 根据文件路径获取文件信息，返回一个fs.Stats对象
    const stats = fs.statSync(filedir);
    const isFile = stats.isFile(); // 是文件
    if (isFile) {
      const fileContent = fs.readFileSync(filedir).toString('utf-8');
      const externalize = fileContent.match(/\/\* externalize: (.+) \*\//);
      if (externalize) {
        //   console.log(externalize)
        externalFiles[externalize[1]] = filedir;
        //   console.log(externalFiles);
      }
    }
    const isDir = stats.isDirectory(); // 是文件夹
    if (isDir) {
      const dirExternalFiles = fileDisplay(filedir);
      // console.log('dirExternalFiles', dirExternalFiles);
      Object.assign(externalFiles, dirExternalFiles);// 递归，如果是文件夹，就继续遍历该文件夹下面的文件
      // console.log('assign', externalFiles);
    }
  }
  return externalFiles;
}

function findIncludedAndExcludedElements(arrayA, arrayB) {
  const excludedElements = [];

  for (let i = 0; i < arrayB.length; i++) {
    const currentElementB = arrayB[i];

    // 检查当前元素是否在数组A中
    if (!arrayA.some(elementA => elementA?.componentCode === currentElementB)) {
      excludedElements.push(currentElementB);
    }
  }

  // 找到数组A中包含但数组B中没有的部分
  const uniqueToA = arrayA.filter(elementA => !arrayB.some(elementB => elementB === elementA?.componentCode)).map(r => r.recordId);
  return {
    excluded: excludedElements?.map(r => ({ fields: { serviceCode: context.packageInfo.routeName, componentCode: r } })),
    uniqueToA,
  };
}
function collectComponents(obj) {
  axios.get(apiUrl, {
    headers: {
      Authorization: `Bearer ${apiToken}`,
    },
  }).then(response => {
    const locallist = response?.data?.data?.records?.map(r => ({ ...r?.fields, recordId: r?.recordId }))
      ?.filter(i => i.serviceCode === context.packageInfo.routeName);
    const { excluded, uniqueToA } = findIncludedAndExcludedElements(locallist, Object.keys(obj));
    if (excluded?.length) {
      const requestDataPost = {
        records: excluded,
        fieldKey: 'name',
      };
      axios.post(apiUrl, requestDataPost, {
        headers: {
          Authorization: `Bearer ${apiToken}`,
          'Content-Type': 'application/json',
        },
      })
        .catch(error => {
        // 处理错误
          console.error('Axios POST error:', error);
        });
    }
    if (uniqueToA?.length) {
      axios.delete(`https://table.zknow.com/fusion/v1/datasheets/dst6zjjsfkkpDQuQub/records?recordIds=${uniqueToA.join('&recordIds=')}`, {
        headers: {
          Authorization: `Bearer ${apiToken}`,
          'Content-Type': 'application/json',
        },
      })
        .catch(error => {
          // 处理错误
           console.error('Axios POST error:', error);
        });
    }
  })
    .catch(error => {
    // 处理错误
       console.error('Axios POST error:', error);
    });
}
export default function getExternalizeExposes() {
  const { packageInfo, src, projectConfig: { exposes } } = context;
  const obj = fileDisplay(path.resolve(src));
  // collectComponents(obj)
  return obj;
};
// getExternalizeExposes();
