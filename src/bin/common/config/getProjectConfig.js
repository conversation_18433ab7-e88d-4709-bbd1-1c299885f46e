import fs from 'fs';

const defaultConfig = {
  port: 9090,
  routeType: 'HashRouter',
  theme: {
    'primary-color': '#2979FF',
  },
  output: './dist',
  htmlTemplate: 'index.template.html',
  devServerConfig: {},
  browsers: [
    'last 2 versions',
    'Firefox ESR',
    '> 0.25%',
  ],
  babelConfig(config, mode, env) {
    return config;
  },
  webpackConfig(config, mode, env) {
    return config;
  },
  enterPoints(mode, env) {
    return {};
  },
  entryName: 'index',
  root: '/',
  routes: {},
  modules: [],
  local: true,
  server: '',
  clientid: 'localhost',
  webSocketServer: 'http://localhost:8080',
  // eslint-disable-next-line no-chinese/no-chinese
  titlename: '甄知科技 | 解决服务流率挑战',
  favicon: 'favicon.png',
  dashboard: false,
  guide: false,
  // proxyTarget: 'http://localhost:8080',
  distBasePath: './src/main/resources/lib',
  htmlPath: './src/main/resources/WEB-INF/view',
  homePath: undefined,
  // menuTheme: 'light',
  // resourcesLevel: ['site', 'user'],
  disableParallel: false,
};

export default function getProjectConfig(configFile) {
  const customizedConfig = fs.existsSync(configFile) ? require(configFile) : {};
  return {
    ...defaultConfig,
    ...customizedConfig,
  };
}
