import { join, resolve } from 'path';
import fs from 'fs';
import webpack from 'webpack';
import { ModuleFederationPlugin } from '@module-federation/enhanced/webpack';
import CaseSensitivePathsPlugin from 'case-sensitive-paths-webpack-plugin';
import FriendlyErrorsWebpackPlugin from 'friendly-errors-webpack-plugin';
import OptimizeCSSAssetsPlugin from 'optimize-css-assets-webpack-plugin';
import UglifyJsPlugin from 'terser-webpack-plugin';
import TerserPlugin from 'terser-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import DotEnvRuntimePlugin from 'dotenv-runtime-plugin';
import ThemeColorReplacer from 'webpack-theme-color-replacer';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import chalk from 'chalk';
import postcssNormalize from 'postcss-normalize';
import WebpackBar from 'webpackbar';
import HtmlWebpackPlugin from 'html-webpack-plugin';
// import { GenerateSW } from 'workbox-webpack-plugin';
import getBabelCommonConfig from './getBabelCommonConfig';
import getTSCommonConfig from './getTSCommonConfig';
import getExternalizeExposes from './getExternalizeExposes';
import context from '../context';
import getRouteName from '../utils/getRouteName';
import getRouteIndex from '../utils/getRouteIndex';
import transformMain from '../utils/transformMain';
import { webpack as alias } from './getAlias';
import getES6Modules from './getES6Modules';
import getDefaultTheme from './getDefaultTheme';
import generateEnvironmentVariable from '../generateEnvironmentVariable';

// const { ModuleFederationPlugin } = require('webpack').container;
const deps = require('../../../../package.json').dependencies;

const babelImports = require('./getBabelImports');
const sharedModules = require('./getSharedModules');

const uiModules = {
  'choerodon-ui/dataset': ['c7n-dataset', 'choerodon-ui'],
  'choerodon-ui/dataset/data-set': ['c7n-dataset-data-set', 'choerodon-ui'],
  'choerodon-ui/shared': ['c7n-shared', 'choerodon-ui'],
};

const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP === 'true';
const jsFileName = '[name].[fullhash:8].js';
const jsChunkFileName = 'chunks/[name].[chunkhash:5].chunk.js';
const cssFileName = '[name].[contenthash:8].css';
const cssChunkFileName = '[name].[contenthash:8].chunk.css';
const cssColorFileNameDefault = 'theme-colors.css';
const assetFileName = 'assets/[name].[hash:8].[ext]';
let processTimer;

const eagerList = [];

let workerLoader;
try {
  workerLoader = require.resolve('../../../../node_modules/worker-loader');
} catch (e) {
  workerLoader = require.resolve('worker-loader');
}

// function normalizeToSassVariables(modifyVarsOptions) {
//   const { modifyVars, ...options } = modifyVarsOptions;
//   if (modifyVars) {
//     options.data = Object.keys(modifyVars).map((key) => `$${key}: ${modifyVars[key]};`).join('');
//   }
//   return options;
// }

function getFilePath(file, master, mode) {
  const { isDev } = context;
  const filePath = resolve(file);
  if (fs.existsSync(filePath)) {
    return filePath;
  } else if (isDev || mode === 'start') {
    return require.resolve(`${master}/lib/${file}`)
  } else {
    return join(__dirname, '../../../', file);
  }
}

export default function getWebpackCommonConfig() {
  const {
    packageInfo, src, lib, mode, env, dotenv,
    projectConfig: {
      babelConfig, browsers, disableParallel, enterPoints, entryName, exposes, htmlTemplate,
      output, outward, port, theme, titlename, resourcesLevel, routeMap, cssColorFileName,
      projectType, master,
    },
  } = context;
  const isEnvProduction = env === 'production';
  const isEnvDevelopment = env === 'development';
  const entryPath = join(__dirname, '..', '..', '..', '..', 'tmp', `bootstrap.${entryName}.js`);
  const babelOptions = babelConfig({
    ...getBabelCommonConfig({ modules: 'commonjs', mode, browsers }),
    cacheDirectory: true,
    cacheCompression: false,
    compact: isEnvProduction,
  }, mode, env);
  const tsOptions = getTSCommonConfig();
  const routeName = getRouteName(packageInfo);
  const lessOptions = {
    javascriptEnabled: true,
    modifyVars: { ...getDefaultTheme(env), ...theme },
    sourceMap: isEnvProduction ? shouldUseSourceMap : isEnvDevelopment,
  };
  const mergedEnterPoints = {
    NODE_ENV: env,
    RESOURCES_LEVEL: Array.isArray(resourcesLevel) ? resourcesLevel.join(',') : resourcesLevel,
    OUTWARD: outward,
    routeMap,
    ...enterPoints(mode, env),
    projectType,
  };
  if (mode === 'start') {
    mergedEnterPoints[packageInfo.routeName] = `http://localhost:${port}`;
  }
  const envStr = generateEnvironmentVariable(mergedEnterPoints, packageInfo, mode === 'start');

  const defines = Object.keys(mergedEnterPoints).reduce((obj, key) => {
    obj[`process.env.${key}`] = JSON.stringify(process.env[key] || mergedEnterPoints[key]);
    return obj;
  }, {});

  // MF 2.0 优化的 shared 配置
  const shared = {};
  Object.entries(sharedModules).forEach(([name, version]) => {
    const option = {
      singleton: true,
      requiredVersion: version,
      // MF 2.0 性能优化：核心库延迟加载
      eager: eagerList.includes(name) || ['react', 'react-dom'].includes(name) ? false : true,
      // 严格版本控制，提高稳定性
      strictVersion: ['react', 'react-dom', 'mobx', 'mobx-react'].includes(name),
    };
    shared[name] = option;
  });

  const getStyleLoaders = (cssOptions, preProcessor) => {
    const loaders = [
      isEnvDevelopment && require.resolve('style-loader'),
      isEnvProduction && MiniCssExtractPlugin.loader,
      {
        loader: require.resolve('css-loader'),
        options: cssOptions,
      },
      {
        loader: require.resolve('postcss-loader'),
        options: {
          ident: 'postcss',
          plugins: () => [
            require('postcss-flexbugs-fixes'),
            require('postcss-preset-env')({
              browsers,
              autoprefixer: {
                flexbox: 'no-2009',
              },
              stage: 3,
            }),
            postcssNormalize(),
          ],
          sourceMap: isEnvProduction ? shouldUseSourceMap : isEnvDevelopment,
        },
      },
      {
        loader: 'px2rem-loader',
        options: {
          remUnit: 100,
          remPrecision: 6 // px转rem小数点保留的位置
        }
      },
    ].filter(Boolean);
    if (preProcessor) {
      loaders.push(preProcessor);
    }
    return loaders;
  };

  const getSharedUiLib = (uiModule, moduleName) => {
    const sharedUiLib = {};
    // console.log(path.resolve('node_modules/'));
    const uiLibs = fs.readdirSync(resolve('node_modules/', uiModule));
    uiLibs.forEach(uiLib => {
      const uiLibInfo = fs.statSync(resolve('node_modules/', uiModule, uiLib));
      if (uiLibInfo.isDirectory()) {
        // const subDir = fs.readdirSync(resolve('../../', 'node_modules/', uiModule, uiLib));
        Object.assign(sharedUiLib, getSharedUiLib(`${uiModule}/${uiLib}`));
        fs.readdirSync(resolve('node_modules/', uiModule, uiLib)).forEach(dep => {
          if (dep.endsWith('.js')) {
            // console.log(`${uiModule}/${uiLib}/${dep.split('.js')[0]}`);
            sharedUiLib[`${uiModule}/${uiLib}/${dep.split('.js')[0]}`] = {
              // eager: true,
              singleton: true,
              requiredVersion: false,
            };
          }
        });
        if (fs.readdirSync(resolve('node_modules/', uiModule, uiLib)).includes('index.js')) {
          sharedUiLib[`${uiModule}/${uiLib}`] = {
            // eager: true,
            singleton: true,
            requiredVersion: false,
          };
        }
      }
    });
    return sharedUiLib;
  };

  const getSharedUiComponent = (uiModule) => ({
    [`${uiModule}`]: {
      singleton: true,
      requiredVersion: false,
    },
  });

  // TODO: 兼容性处理，兼容猪齿鱼共享问题
  const getInstallExposes = () => {
    let installExposes = {};
    if (projectType === 'choerodon' && (!packageInfo.nonInstall || packageInfo.nonInstall === 'false')) {
      installExposes = {
        './install': packageInfo.install && typeof packageInfo.install === 'string' ? transformMain(packageInfo.install, lib, src) : './react/install.js',
      };
    }
    return installExposes;
  };
  // 构建完整的 shared 配置
  const completeSharedConfig = {
    // 核心依赖
    ...shared,

    // 框架特定依赖
    [master]: {
      singleton: true,
      requiredVersion: false,
    },

    // UI 组件库
    'choerodon-ui-font': {
      singleton: true,
      requiredVersion: false,
    },
    'choerodon-ui/dataset': {
      singleton: true,
      requiredVersion: false
    },
    'choerodon-ui/shared': {
      singleton: true,
      requiredVersion: false
    },

    // 动态 UI 模块
    ...Object.keys(babelImports).reduce((obj, key) => ({
      ...obj,
      ...getSharedUiLib(`${key}/lib`, babelImports[key][1]),
    }), {}),
    ...Object.keys(uiModules).reduce((obj, key) => ({
      ...obj,
      ...getSharedUiLib(`${key}`, uiModules[key][1]),
    }), {}),

    // 业务组件库
    '@zknow/utils': {
      singleton: true,
      requiredVersion: false,
    },
    '@zknow/components': {
      singleton: true,
      requiredVersion: false,
    },

    // 编辑器相关
    ckeditor5: {
      singleton: true,
      requiredVersion: false,
    },
    '@yqcloud/ckeditor5': {
      singleton: true,
      requiredVersion: false,
    },
    '@yqcloud/ckeditor5-react': {
      singleton: true,
      requiredVersion: false,
    },
  };

  // MF 2.0 优化配置
  const moduleFederationPluginConfig = {
    name: routeName,
    // 升级为 manifest 格式 - MF 2.0 新特性
    filename: 'importManifest.json',
    exposes: getRouteIndex(packageInfo) && {
      [`./${routeName}`]: transformMain(getRouteIndex(packageInfo), lib, src),
      ...getExternalizeExposes(),
      ...exposes,
    },
    // 使用对象格式的 shared 配置，更符合 MF 2.0 规范
    shared: completeSharedConfig,

    // MF 2.0 新特性配置
    manifest: true, // 启用 manifest 功能

    // 实验性功能 - 启用新的运行时架构
    experiments: {
      federationRuntime: 'hoisted',
    },

    // 开发模式配置
    ...(mode === 'start' && {
      dev: {
        port,
        host: 'localhost',
      },
    }),
  };

  return {
    mode: env,
    bail: isEnvProduction,
    devtool: isEnvProduction
      ? shouldUseSourceMap
        ? 'source-map'
        : false
      : isEnvDevelopment && 'cheap-module-source-map',
    entry: { [entryName]: entryPath },
    output: {
      path: isEnvProduction ? resolve(output) : undefined,
      filename: jsFileName,
      chunkFilename: jsChunkFileName,
      globalObject: 'this',
      // MF 2.0 优化：支持自动 publicPath
      publicPath: mode === 'start' ? `http://localhost:${port}/` : 'auto',
      // 确保模块联邦的正确加载
      uniqueName: routeName,
    },
    optimization: {
      minimize: isEnvProduction,
      minimizer: disableParallel ? [
        new UglifyJsPlugin({
          cache: true,
          parallel: false,
        }),
        new OptimizeCSSAssetsPlugin({}),
      ] : [
        new TerserPlugin({
          cache: true,
          terserOptions: {
            parse: {
              ecma: 8,
            },
            compress: {
              ecma: 5,
              warnings: false,
              comparisons: false,
              inline: 2,
            },
            mangle: {
              safari10: true,
            },
            output: {
              ecma: 5,
              comments: false,
              ascii_only: true,
            },
          },
        }),
      ],
    },
    snapshot: {
      managedPaths: [],
    },
    watchOptions: {
      // 对于node_modules仅监听微服务前缀的文件变化
      ignored: /node_modules\/(?!(@zknow|@yqcloud|@choerodon)\/.+)/,
    },
    resolve: {
      mainFields: ['browser', 'main', 'module'],
      modules: ['node_modules', join(__dirname, '../../node_modules')],
      extensions: ['.web.tsx', '.web.ts', '.web.jsx', '.web.js', '.ts', '.tsx', '.js', '.jsx', '.json'],
      alias: {
        ...alias,
        events: 'events',
        'stream': 'stream-browserify',
        'timers': 'timers-browserify',
        'os': 'os-browserify/browser',
        "crypto": "crypto-browserify",
      },
    },
    resolveLoader: {
      modules: ['node_modules', join(__dirname, '../../node_modules')],
      alias,
    },
    module: {
      strictExportPresence: true,
      // noParse: [/moment.js/],
      rules: [
        {
          oneOf: [
            {
              test: /\.js$/,
              exclude: /node_modules\/(?!react-draggable)/,
              loader: require.resolve('babel-loader'),
              options: babelOptions,
            },
            {
              test: [/\.(mjs|jsx)$/, ...getES6Modules().map(module => new RegExp(`node_modules[/\\\\]${module}`))],
              loader: require.resolve('babel-loader'),
              options: babelOptions,
            },
            {
              test: /\.tsx?$/,
              use: [{
                loader: require.resolve('babel-loader'),
                options: babelOptions,
              }, {
                loader: require.resolve('ts-loader'),
                options: {
                  transpileOnly: true,
                  compilerOptions: tsOptions,
                },
              }],
            },
          ],
        },
        {
          test: /\.worker\.(c|m)?js$/i,
          loader: workerLoader,
          options: {
            inline: 'no-fallback',
          },
        },
        {
          test: [
            /\.bmp$/,
            /\.gif$/,
            /\.jpe?g$/,
            /\.png$/,
            /\.eot$/,
            /\.woff2?$/,
            /\.ttf$/,
            /\.svg$/,
            /\.mp3$/,
          ],
          loader: require.resolve('url-loader'),
          options: {
            limit: 10000,
            name: assetFileName,
          },
          exclude: /\.sprite\.svg$/,
        },
        {
          test: /\.svg$/,
          loader: 'svg-sprite-loader',
          include: /\.sprite\.svg$/,
        },
        {
          test: /\.css$/,
          use: getStyleLoaders({
            importLoaders: 1,
            modules: {
              auto: (resourcePath) => resourcePath.endsWith('module.less'),  // 匹配.less文件来进行css模块化。
              localIdentName: '[name]_[local]_[hash:base64:4]',
            },
            // sourceMap: isEnvProduction
            //   ? shouldUseSourceMap
            //   : isEnvDevelopment,
          }),
          sideEffects: true,
        },
        {
          oneOf: [{
            localIdentName: '[name]_[local]_[hash:base64:4]',
          }, false].map(autoModule => ({
            test: /\.less$/,
            resourceQuery: autoModule ? /modules/ : undefined,
            use: getStyleLoaders({
              importLoaders: 3,
              modules: autoModule,
            }, {
              loader: require.resolve('less-loader'),
              options: lessOptions,
            }),
            sideEffects: true,
          }))
        }
        // {
        //   test: /\.(scss|sass)$/,
        //   use: getStyleLoaders({
        //     importLoaders: 3,
        //     // sourceMap: isEnvProduction
        //     //   ? shouldUseSourceMap
        //     //   : isEnvDevelopment,
        //   }, {
        //     loader: require.resolve('sass-loader'),
        //     options: normalizeToSassVariables(lessOptions),
        //   }),
        //   sideEffects: true,
        // },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        title: process.env.TITLE_NAME || titlename,
        template: getFilePath(htmlTemplate, master, mode),
        inject: true,
        favicon: '', // getFilePath(favicon),
        env: envStr,
        disableConsole: isEnvProduction ? `<script>if(typeof console !=='undefined'){console.log=console.warn=function(){}}</script>` : '',
        ...(isEnvProduction ? {
          minify: {
            html5: true,
            collapseWhitespace: true,
            removeComments: true,
            removeTagWhitespace: true,
            removeEmptyAttributes: true,
            removeStyleLinkTypeAttributes: true,
          },
        } : undefined),
      }),
      // 不使用 GenerateSW，yqc 无法做到离线使用，所以没必要使用 Service Worker 缓存功能
      // isEnvProduction && new GenerateSW({
      //   // 定义缓存的文件模式
      //   include: [/\.html$/, /\.js$/, /\.css$/],
      //   // 忽略的文件模式
      //   exclude: [/sw\.js$/],
      //   // 服务工作线程的输出文件名
      //   swDest: 'sw.js',
      //   // 配置运行时缓存
      //   runtimeCaching: [
      //     {
      //       urlPattern: new RegExp('^https://'),
      //       handler: 'StaleWhileRevalidate',
      //       options: {
      //         cacheName: 'external-resources',
      //         expiration: {
      //           maxEntries: 50,
      //           maxAgeSeconds: 30 * 24 * 60 * 60 // 30 days
      //         }
      //       }
      //     }
      //   ]
      // }),
      new CopyWebpackPlugin({
        patterns: [
          { from: `${src}/assets/sdk`, to: `${resolve(output)}/assets/sdk`, noErrorOnMissing: true },
          { from: `${src}/manifest.json`, to: `${resolve(output)}/manifest.json`, noErrorOnMissing: true },
          { from: `${src}/pwa-icons`, to: `${resolve(output)}/pwa-icons`, noErrorOnMissing: true },
          // { from: `${src}/sw.js`, to: `${resolve(output)}/sw.js`, noErrorOnMissing: true },
        ],
        options: {
          concurrency: 100,
        },
      }),
      new ModuleFederationPlugin(moduleFederationPluginConfig),
      new webpack.ProgressPlugin((percentage, msg, addInfo) => {
        const stream = process.stderr;
        if (stream.isTTY) {
          if (stream.isTTY && percentage < 0.71) {
            stream.cursorTo(0);
            stream.write(`📦  ${chalk.magenta(msg)} (${chalk.magenta(addInfo)})`);
            stream.clearLine(1);
          } else if (percentage === 1) {
            // eslint-disable-next-line no-console
            console.log(chalk.green('\nwebpack: bundle build is now finished.'));
          }
        } else {
          const outputStr = '📦  bundleing!';
          if (percentage !== 1 && !processTimer) {
            // eslint-disable-next-line no-console
            console.log(`${outputStr}  ${new Date()}`);
            processTimer = setInterval(() => {
              // eslint-disable-next-line no-console
              console.log(`${outputStr}  ${new Date()}`);
            }, 1000 * 30);
          } else if (percentage === 1) {
            // eslint-disable-next-line no-console
            console.log(chalk.green('\nwebpack: bundle build is now finished.'));
            if (processTimer) {
              clearInterval(processTimer);
            }
          }
        }
      }),
      new FriendlyErrorsWebpackPlugin(),
      new ThemeColorReplacer({
        fileName: cssColorFileName || cssColorFileNameDefault,
        matchColors: [
          '#001866', // color-10
          '#01268c', // color-9
          '#0b3db3', // color-8
          '#1858d9', // color-7
          '#2979ff', // primary-color, color-6
          '#5297ff', // color-5
          '#7ab4ff', // color-4
          '#a3ceff', // color-3
          '#cce6ff', // color-2
          '#f0f8ff', // color-1
          '#ecf4ff', // primary-4 14%
          '#dfedff', // primary-4 24%
          '#f8faff', // primary-color 3%
          '#f2f6ff', // primary-color 6%
          '#e9f1ff', // primary-color 10%
          '#e5eeff', // primary-color 12%
          '#d4e4ff', // primary-color 20%
          '#5393ff', // primary-color 80%
        ],
        isJsUgly: isEnvProduction,
      }),
      isEnvProduction && new webpack.LoaderOptionsPlugin({
        minimize: true,
      }),
      isEnvDevelopment && new DotEnvRuntimePlugin({
        entry: dotenv,
      }),
      isEnvDevelopment && new webpack.HotModuleReplacementPlugin(),
      isEnvDevelopment && new CaseSensitivePathsPlugin(),
      isEnvProduction && new MiniCssExtractPlugin({
        ignoreOrder: true,
        filename: cssFileName,
        chunkFilename: cssChunkFileName,
      }),
      new webpack.DefinePlugin({
        ...defines,
        C7NHasModule: (string) => {
          const map = {
            // TODO: 兼容猪齿鱼： 临时加上如此判断需求插件是否存在
            '@choerodon/backlog': 'remote_agile',
            '@choerodon/agile-pro': 'remote_agile',
            '@choerodon/asgard': 'remote_asgard',
            '@choerodon/base-saas': 'remote_saas',
            '@choerodon/base-pro': 'remote_basePro',
            '@choerodon/base-business': 'remote_baseBusiness',
            '@choerodon/base': 'remote_base',
            '@choerodon/devops': 'remote_devops',
            '@choerodon/manager': 'remote_manager',
            '@choerodon/market': 'remote_market',
            '@choerodon/notify': 'remote_notify',
            '@choerodon/testmanager-pro': 'remote_testManager',
            '@choerodon/code-repo': 'remote_rducm',
            '@choerodon/doc-repo': 'remote_rdudm',
            '@choerodon/prod-repo': 'remote_rdupm',
            '@choerodon/hrds-qa': 'remote_rdqam',
            '@choerodon/knowledge': 'remote_knowledge',
          };
          const flag = map[string];
          // eslint-disable-next-line no-underscore-dangle
          if (flag && window._env_?.[flag]) {
            return true;
          }
          return false;
        },
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      }),
      new WebpackBar({
        name: '🚚  ZKnow',
        color: theme['primary-color'] || '#2979ff',
      }),
    ].filter(Boolean),
  };
}
