import { getCookieToken, removeAccessToken } from '../access-token';
import { ACCESS_TOKEN, AUTH_HOST } from '../constants';

/**
 * 登出
 */
export default function logoutC7n() {
  const token = getCookieToken();
  let logoutUrl = `${AUTH_HOST}/logout`;
  if (token) {
    logoutUrl += `?${ACCESS_TOKEN}=${getCookieToken()}`;
  }
  removeAccessToken();
  window.localStorage.removeItem('lastClosedId');
  sessionStorage.clear();
  window.location = logoutUrl;
}
