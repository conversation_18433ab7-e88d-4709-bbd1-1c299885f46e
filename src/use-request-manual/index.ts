import { useRequest } from 'ahooks';
import { Options } from 'ahooks/lib/useRequest/src/types';

/**
 * 统一请求调用方法，内置拦截，默认手动触发
 * @param fetcher 请求函数
 * @param options ahooks/useRequest 的选项，可参考官方文档
 * @param interceptResponse 是否拦截请求
 * @return {{cancel: Fetch<unknown, any[]>["cancel"], mutate: Fetch<unknown, any[]>["mutate"], data: unknown, refresh: Fetch<unknown, any[]>["refresh"], refreshAsync: Fetch<unknown, any[]>["refreshAsync"], run: Fetch<unknown, any[]>["run"], trigger: ((function(...[*]): Promise<unknown>)|*), loading: boolean, error: Error, params: [] | any[], runAsync: Fetch<unknown, any[]>["runAsync"]}}
 */
export default function useRequestManual(
  fetcher: any,
  options?: Options<any, any>,
  interceptResponse = true,
) {
  const { runAsync, ...rest } = useRequest(fetcher, {
    manual: true,
    ...options,
  });
  const trigger = async (...params: any) => {
    if (!interceptResponse) {
      return runAsync(...params);
    }
    try {
      const res: any = await runAsync(...params);
      if (res?.failed) {
        // TODO：失败处理
      } else {
        // TODO：成功处理
      }
    } catch (e) {
      // TODO：错误处理
    }
  };
  return {
    trigger,
    runAsync,
    ...rest,
  };
}
