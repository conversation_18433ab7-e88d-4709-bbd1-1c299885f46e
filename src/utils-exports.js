/**
 * @zknow/utils 导出模拟
 * 这个文件模拟了 @zknow/utils 包的导出结构
 * 在实际的 @zknow/utils 项目中，这些导出应该在主 index.js 文件中
 */

// 现有的 @zknow/utils 功能（这些需要从实际的 @zknow/utils 中导入）
export { default as loadComponent } from './load-component';
export { default as useManifest } from './use-manifest';

// 新增的 MF 2.0 功能
export { default as loadRemoteModule } from './load-remote-module';
export { default as checkRemoteAvailability } from './check-remote-availability';
export { 
  initMFRuntime, 
  getLoadedRemotes, 
  clearFederationCache, 
  setupMFErrorHandling 
} from './mf-runtime';

// 其他现有功能（示例，实际需要根据真实的 @zknow/utils 调整）
// export { default as request } from './request';
// export { default as storage } from './storage';
// export { default as auth } from './auth';
// ... 其他工具函数
