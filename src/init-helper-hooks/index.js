import Store from '../store';

export function useDataSet(code) {
  return Store.get(Store.LC_APP).useDataSet(code);
}

export function usePageContext(code) {
  return Store.get(Store.LC_APP).usePageContext(code);
}

export function useLogicFlow(code) {
  return Store.get(Store.LC_APP).useLogicFlow(code);
}

export function usePageParams(code) {
  return Store.get(Store.LC_APP).usePageParams(code);
}

export function useSection(code) {
  return Store.get(Store.LC_APP).useSection(code);
}

export function useUiFlow(code) {
  return Store.get(Store.LC_APP).useUiFlow(code);
}

export default function initHelperHooks(initHooks) {
  Store.set(Store.LC_APP, initHooks);
}

