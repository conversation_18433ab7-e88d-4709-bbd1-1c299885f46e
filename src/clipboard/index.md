---
title: copy
nav:
  title: util
---

# copy

复制信息到剪切板

```js
import { copy } from '@zknow/utils';

copy('Text');

// Copy with options
copy('Text', {
  debug: true,
  message: 'Press #{key} to copy',
});
```

`copy(text: string, options: object): boolean` &mdash; tries to copy text to clipboard. Returns `true` if no additional keystrokes were required from user (so, `execCommand`, I<PERSON>'s `clipboardData` worked) or `false`.

| Value           | Default                            | Notes                                                                                                                                                                                 |
| --------------- | ---------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| options.debug   | false                              | `Boolean`. Optional. Enable output to console.                                                                                                                                        |
| options.message | Copy to clipboard: `#{key}`, Enter | `String`. Optional. Prompt message. `*`                                                                                                                                               |
| options.format  | "text/html"                        | `String`. Optional. Set the MIME type of what you want to copy as. Use `text/html` to copy as HTML, `text/plain` to avoid inherited styles showing when pasted into rich text editor. |
| options.onCopy  | null                               | `function onCopy(clipboardData: object): void`. Optional. Receives the clipboardData element for adding custom behavior such as additional formats                                    |
