/* eslint-disable no-underscore-dangle */
import React from 'react';
import { loadRemoteModule, checkRemoteAvailability } from '@zknow/utils';
import Loading from '../loading';

const cache = new Map();

// MF 2.0 组件获取函数
function getComponent({ scope, module }, ErrorComponent = null, setLoad, enableCache = true) {
  if (enableCache) {
    const scopeItem = cache.get(scope) || new Map();
    cache.set(scope, scopeItem);
    const component = scopeItem.get(module);
    if (component) {
      return component;
    }
  }

  // 直接使用 MF 2.0 的加载方式
  const lazyComponent = React.lazy(async () => {
    try {
      if (setLoad) setLoad(false); // 开始加载
      const moduleResult = await loadRemoteModule(scope, `./${module}`);
      if (setLoad) setLoad(true); // 加载完成
      return moduleResult;
    } catch (error) {
      console.error('[MF 2.0] 组件加载失败:', error);
      if (setLoad) setLoad(true); // 加载完成（失败）
      return {
        default: () => ErrorComponent || (
          <div style={{
            padding: '16px',
            border: '1px solid #ff4d4f',
            borderRadius: '6px',
            backgroundColor: '#fff2f0',
            color: '#ff4d4f',
            textAlign: 'center',
          }}>
            ⚠️ 组件加载失败: {scope}/{module}
          </div>
        ),
      };
    }
  });

  if (enableCache) {
    const scopeItem = cache.get(scope) || new Map();
    cache.set(scope, scopeItem);
    scopeItem.set(module, lazyComponent);
  }

  return lazyComponent;
}

function ExternalComponent(props) {
  const {
    system,
    notFound,
    ErrorComponent,
    fallback = <Loading />,
    setLoad,
    // MF 2.0 属性
    onError,                  // 错误回调
    onLoad,                   // 加载成功回调
    retryCount = 0,           // 重试次数
    timeout = 30000,          // 超时时间
    enableCache = true,       // 是否启用缓存
    checkAvailability = false, // 是否检查服务可用性
    ...restProps
  } = props;

  const [isAvailable, setIsAvailable] = React.useState(true);
  const [availabilityChecked, setAvailabilityChecked] = React.useState(!checkAvailability);

  // 检查远程服务可用性
  React.useEffect(() => {
    if (checkAvailability && system?.scope) {
      checkRemoteAvailability(system.scope)
        .then(available => {
          setIsAvailable(available);
          setAvailabilityChecked(true);
          if (!available && onError) {
            onError(new Error(`远程服务不可用: ${system.scope}`));
          }
        })
        .catch(err => {
          setIsAvailable(false);
          setAvailabilityChecked(true);
          if (onError) onError(err);
        });
    }
  }, [system?.scope, checkAvailability, onError]);

  if (!system) {
    return <h2>Not system specified</h2>;
  }

  if (!availabilityChecked) {
    return fallback;
  }

  if (!isAvailable) {
    return notFound || (
      <div style={{
        padding: '16px',
        border: '1px solid #ff4d4f',
        borderRadius: '6px',
        backgroundColor: '#fff2f0',
        color: '#ff4d4f',
        textAlign: 'center',
      }}>
        ⚠️ 远程服务不可用: {system.scope}
      </div>
    );
  }

  const Component = getComponent(system, ErrorComponent, setLoad, enableCache);

  // MF 2.0 增强的错误边界
  const ErrorBoundary = ({ children }) => {
    const [hasError, setHasError] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [retryAttempts, setRetryAttempts] = React.useState(0);
    const timeoutRef = React.useRef(null);

    // 清理定时器
    React.useEffect(() => {
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    // 超时处理
    React.useEffect(() => {
      if (timeout > 0 && !hasError) {
        timeoutRef.current = setTimeout(() => {
          const timeoutError = new Error(`组件加载超时: ${system.scope}/${system.module}`);
          setHasError(true);
          setError(timeoutError);
          if (onError) onError(timeoutError);
        }, timeout);
      }
    }, [timeout, hasError]);

    // 自动重试逻辑
    React.useEffect(() => {
      if (hasError && retryAttempts < retryCount) {
        const timer = setTimeout(() => {
          setHasError(false);
          setError(null);
          setRetryAttempts(prev => prev + 1);

          // 清除缓存，强制重新加载
          if (!enableCache) {
            const scopeItem = cache.get(system.scope);
            if (scopeItem) {
              scopeItem.delete(system.module);
            }
          }
        }, 2000);
        return () => clearTimeout(timer);
      }
    }, [hasError, retryAttempts]);

    // 手动重试函数
    const handleRetry = React.useCallback(() => {
      setHasError(false);
      setError(null);
      setRetryAttempts(prev => prev + 1);

      // 清除缓存，强制重新加载
      const scopeItem = cache.get(system.scope);
      if (scopeItem) {
        scopeItem.delete(system.module);
      }
    }, [system.scope, system.module]);

    if (hasError) {
      const errorElement = ErrorComponent ? (
        <ErrorComponent
          error={error}
          retry={handleRetry}
          retryAttempts={retryAttempts}
          maxRetries={retryCount}
        />
      ) : (
        <div style={{
          padding: '16px',
          border: '1px solid #ff4d4f',
          borderRadius: '6px',
          backgroundColor: '#fff2f0',
          color: '#ff4d4f',
          textAlign: 'center',
        }}>
          <div>⚠️ 组件加载失败</div>
          <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
            {system.scope}/{system.module}
          </div>
          <div style={{ fontSize: '12px', marginTop: '4px', color: '#999' }}>
            {error?.message}
          </div>
          {retryCount > 0 && (
            <div style={{ marginTop: '8px' }}>
              {retryAttempts < retryCount ? (
                <div style={{ fontSize: '12px' }}>
                  自动重试中... ({retryAttempts + 1}/{retryCount + 1})
                </div>
              ) : (
                <button
                  onClick={handleRetry}
                  style={{
                    padding: '4px 8px',
                    border: '1px solid #ff4d4f',
                    backgroundColor: 'transparent',
                    color: '#ff4d4f',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                  }}
                >
                  手动重试
                </button>
              )}
            </div>
          )}
        </div>
      );

      if (onError && retryAttempts === 0) {
        onError(error);
      }

      return errorElement;
    }

    return (
      <React.Suspense fallback={fallback}>
        {children}
      </React.Suspense>
    );
  };

  return (
    <ErrorBoundary>
      <Component
        {...restProps}
        onComponentLoad={onLoad}
      />
    </ErrorBoundary>
  );
}

export default ExternalComponent;
