/* eslint-disable no-underscore-dangle */
import React from 'react';
import { loadComponent, useManifest } from '@zknow/utils';
import loadRemoteModule from '../load-remote-module';
import Loading from '../loading';

const cache = new Map();

// MF 2.0 增强的组件获取函数
function getComponent({ scope, module }, ErrorComponent = null, setLoad, useMF2 = false) {
  const scopeItem = cache.get(scope) || new Map();
  cache.set(scope, scopeItem);
  const component = scopeItem.get(module);
  if (component) {
    return component;
  }

  let lazyComponent;

  if (useMF2) {
    // 使用 MF 2.0 的加载方式
    lazyComponent = React.lazy(async () => {
      try {
        if (setLoad) setLoad(false); // 开始加载
        const moduleResult = await loadRemoteModule(scope, `./${module}`);
        if (setLoad) setLoad(true); // 加载完成
        return moduleResult;
      } catch (error) {
        console.error('[MF 2.0] 组件加载失败:', error);
        if (setLoad) setLoad(true); // 加载完成（失败）
        return {
          default: () => ErrorComponent || (
            <div style={{
              padding: '16px',
              border: '1px solid #ff4d4f',
              borderRadius: '6px',
              backgroundColor: '#fff2f0',
              color: '#ff4d4f',
              textAlign: 'center',
            }}>
              ⚠️ 组件加载失败: {scope}/{module}
            </div>
          ),
        };
      }
    });
  } else {
    // 保持原有的加载方式（向后兼容）
    lazyComponent = React.lazy(
      loadComponent(scope, module, (error) => {
        console.error(error);
        return {
          default: () => ErrorComponent,
        };
      }, setLoad),
    );
  }

  scopeItem.set(module, lazyComponent);
  return lazyComponent;
}

function ExternalComponent(props) {
  const {
    system,
    notFound,
    ErrorComponent,
    fallback = <Loading />,
    setLoad,
    // MF 2.0 新增属性
    useMF2 = false,           // 是否使用 MF 2.0 加载方式
    onError,                  // 错误回调
    onLoad,                   // 加载成功回调
    retryCount = 0,           // 重试次数
    timeout = 30000,          // 超时时间
  } = props;

  const { ready, failed } = useManifest(system);

  if (!props.system) {
    return <h2>Not system specified</h2>;
  }

  if (failed) {
    return notFound || <span />;
  }

  if (!ready && !useMF2) {
    return fallback || <Loading />;
  }

  const Component = getComponent(system, ErrorComponent, setLoad, useMF2);

  // MF 2.0 增强的错误边界
  const ErrorBoundary = ({ children }) => {
    const [hasError, setHasError] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [retryAttempts, setRetryAttempts] = React.useState(0);

    React.useEffect(() => {
      if (hasError && retryAttempts < retryCount) {
        const timer = setTimeout(() => {
          setHasError(false);
          setError(null);
          setRetryAttempts(prev => prev + 1);
        }, 2000);
        return () => clearTimeout(timer);
      }
    }, [hasError, retryAttempts]);

    if (hasError) {
      const errorElement = ErrorComponent ? (
        <ErrorComponent error={error} retry={() => {
          setHasError(false);
          setError(null);
        }} />
      ) : (
        <div style={{
          padding: '16px',
          border: '1px solid #ff4d4f',
          borderRadius: '6px',
          backgroundColor: '#fff2f0',
          color: '#ff4d4f',
          textAlign: 'center',
        }}>
          <div>⚠️ 组件渲染失败</div>
          <div style={{ fontSize: '12px', marginTop: '8px', opacity: 0.8 }}>
            {system.scope}/{system.module}
          </div>
          {retryAttempts < retryCount && (
            <div style={{ fontSize: '12px', marginTop: '4px' }}>
              正在重试... ({retryAttempts + 1}/{retryCount + 1})
            </div>
          )}
        </div>
      );

      if (onError) {
        onError(error);
      }

      return errorElement;
    }

    return (
      <React.Suspense
        fallback={fallback}
        onError={(error) => {
          setHasError(true);
          setError(error);
        }}
      >
        {children}
      </React.Suspense>
    );
  };

  return (
    <ErrorBoundary>
      <Component
        {...props}
        onComponentLoad={onLoad}
      />
    </ErrorBoundary>
  );
}

export default ExternalComponent;
