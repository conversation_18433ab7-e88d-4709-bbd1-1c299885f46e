import { authorizeC7n, getAccessToken, removeAccessToken } from '@zknow/utils';
import { Upload } from 'choerodon-ui';
import { message } from 'choerodon-ui/pro';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import React from 'react';
import Icon from '../icon';

import './index.less';

const { Dragger } = Upload;

const prefixCls = 'yq-component-drag-upload';

DragUploader.propTypes = {
  children: PropTypes.element.isRequired,
  overrideProps: PropTypes.object,
  name: PropTypes.string,
  record: PropTypes.any,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};

function DragUploader({
  children,
  dragConfig = {},
  overrideProps,
  name,
  record,
  onChange = () => {},
  disabled = false,
  multiple = false,
  fileSize = false,
  tenantId,
}) {
  const { showPlaceHolder = () => true, dragTip = 'Drag And Drop To Upload' } =
    dragConfig;

  let xTenantId = tenantId;

  try {
    // 帮助中心提单,需要提交到 support 租户中
    const isSupport = sessionStorage.getItem('OPEN_SUPPORT_TENANT_FLAG') === 'true';
    if (isSupport) {
      const HELP_CENTER_CONFIG = JSON.parse(sessionStorage.getItem('HELP_CENTER_CONFIG'));
      // supportTenantId
      const supportTenantId = HELP_CENTER_CONFIG?.supportTenantId;
      if (supportTenantId) {
        xTenantId = supportTenantId;
      }
    }
  } catch {
    //
  }

  const uploadProps = {
    headers: {
      'Access-Control-Allow-Origin': '*',
      Authorization: getAccessToken(),
      'x-tenant-id': xTenantId,
    },
    // eslint-disable-next-line no-underscore-dangle
    action: `${window._env_.API_HOST}/hfle/yqc/v1/${xTenantId}/files/secret-multipart`,
    showUploadList: false,
    multiple: false,
    accept: ['image/*'],
    uploadImmediately: false,
    onChange: ({ file }) => {
      const { status, response } = file;
      onChange(response, record);
      if (status === 'done') {
        if (!response?.failed) {
          if (multiple) {
            let oldList = [];
            try {
              oldList = JSON.parse(record.get(name) || '[]');
              if (fileSize) {
                oldList.push(response);
              } else {
                oldList.push(response.fileKey);
              }
            } catch (e) {
              if (fileSize) {
                oldList.push(response);
              } else {
                oldList.push(response.fileKey);
              }
            }
            record.set(name, JSON.stringify(oldList));
          } else {
            record.set(name, !fileSize ? response.fileKey : response);
          }
        } else {
          message.error(response.message);
        }
      } else if (status === 'error') {
        if (response.code === 'error.permission.accessTokenExpired') {
          message.error('登录状态已失效，请退出重新登录');
          setTimeout(() => {
            removeAccessToken();
            authorizeC7n();
          }, 500);
        } else {
          message.error(`${response.message}`);
        }
      }
    },
    ...overrideProps,
  };
  if (disabled) {
    return children;
  }

  return (
    <div name={name}>
      {showPlaceHolder() && (
        <div
          style={{ marginBottom: 4 }}
          className={classNames({
            [`${prefixCls}-drag-wrap-show`]: true,
          })}
        >
          <Dragger {...uploadProps}>
            <div>
              <Icon
                type="uploadTwo"
                className={`${prefixCls}-icon`}
                size="22"
              />
            </div>
            <span style={{ color: '#595959' }}>{dragTip}</span>
          </Dragger>
        </div>
      )}
      {children}
    </div>
  );
}

export default DragUploader;
