---
title: historyPushMenu
nav:
  title: util

---

# historyPushMenu 

菜单跳转方法

```js
import { historyPushMenu } from '@zknow/utils';
MenuStore.setActiveMenu(newMenu);
historyPushMenu(history, path, null, 'replace');
```

| 入参    | 介绍                                 | 类型          |
| ------- | ------------------------------------ | ------------- |
| history | history                              | History       |
| path    | 路由                                 | string        |
| domain  | 域名，如果写入path该字段可以传入null | string        |
| method  | history路由处理方式                  | push\|replace |

# historyReplaceMenu 

菜单跳转方法，对 historyPushMenu 的引用。

```js
export default function historyReplaceMenu(history, path, uri) {
  historyPushMenu(history, path, uri, 'replace');
}
```

| 入参    | 介绍                                    | 类型    |
| ------- | --------------------------------------- | ------- |
| history | history                                 | History |
| path    | 路由                                    | string  |
| uri     | 域名，如果写入 path 该字段可以传入 null | string  |

