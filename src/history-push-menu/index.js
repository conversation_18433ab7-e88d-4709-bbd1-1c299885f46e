import { LOCAL } from '../constants';

export default function historyPushMenu(history, path, domain, method = 'push') {
  if (window.located) {
    console.error('window.located');
    return;
  }
  if (!domain || LOCAL) {
    history[method](path);
  } else if (!path) {
    window.location = `${domain}`;
  } else {
    const reg = new RegExp(domain, 'g');
    if (reg.test(window.location.host)) {
      history[method](path);
    } else {
      window.location = `${domain}/#${path}`;
    }
  }
}
