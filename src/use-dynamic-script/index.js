/* eslint-disable no-console */
import { EventEmitter } from 'events';
import React from 'react';

const eventBus = new EventEmitter();
const loadFlag = {};
const useDynamicScript = (args) => {
  const [ready, setReady] = React.useState(false);
  const [failed, setFailed] = React.useState(false);

  React.useEffect(() => {
    if (!args.url) {
      return;
    }

    // 已加载的资源不再重新加载
    const scripts = Array.from(document.getElementsByTagName('script'));
    const isExist = scripts.find((script) => script.src === args.url);
    if (isExist) {
      if (
        loadFlag[args.url] ||
        args.url?.includes(`localhost:${window.location.port}`) ||
        args.url?.includes(`127.0.0.1:${window.location.port}`)
      ) {
        setReady(true);
      } else {
        eventBus.once(args.url, () => {
          setReady(true);
        });
        // 注册失败事件监听
        if (loadFlag[args.url] !== false) {
          eventBus.once(`Dynamic Script Error: ${args.url}`, () => {
            setFailed(true);
          });
        }
      }
      if (loadFlag[args.url] === false) {
        setFailed(true);
      }
      return () => {
        console.log(`Dynamic Script Removed: ${args.url}`);
        // document.head.removeChild(element);
      };
    }

    const element = document.createElement('script');

    element.src = args.url;
    element.type = 'text/javascript';
    element.async = true;

    setReady(false);
    setFailed(false);

    element.onload = () => {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`Dynamic Script Loaded: ${args.url}`);
      }

      loadFlag[args.url] = true;
      setReady(true);
      eventBus.emit(args.url);
    };

    element.onerror = () => {
      console.error(`Dynamic Script Error: ${args.url}`);
      loadFlag[args.url] = false;
      setReady(false);
      setFailed(true);
      // 加载失败时执行回调，避免同时加载同一资源时，部分时刻拿到的数据不准确
      eventBus.emit(`Dynamic Script Error: ${args.url}`);
    };

    document.head.appendChild(element);

    return () => {
      console.log(`Dynamic Script Removed: ${args.url}`);
      // document.head.removeChild(element);
    };
  }, [args.url]);

  return {
    ready,
    failed,
  };
};

export default useDynamicScript;
