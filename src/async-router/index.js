import React, { useEffect, useState } from 'react';
import { Spin } from 'choerodon-ui';
import esModule from './esModule';

export default function asyncRouter(getComponent, getInjects, extProps, callback) {
  const AsyncRoute = (props) => {
    const [data, setData] = useState({ Cmp: null, loading: true, injects: [] });

    useEffect(() => {
      const streams = [];
      streams.push(
        getComponent && getComponent().then(esModule),
      );
      if (getInjects) {
        if (typeof getInjects === 'function') {
          streams.push(
            getInjects()
              .then(esModule)
              .then((inject) => {
                if (inject.getStoreName) {
                  return { [inject.getStoreName()]: inject };
                }
                return {};
              }),
          );
        } else if (typeof getInjects === 'object') {
          Object.keys(getInjects).forEach((key) => {
            streams.push(
              getInjects[key]()
                .then(esModule)
                .then(inject => ({ [key]: inject })),
            );
          });
        }
      }
      Promise.all(streams)
        .then(([Cmp, ...injects]) => {
          setData({
            Cmp,
            injects,
            loading: false,
          });
          window.__yqcloud_loaded = true;
          if (callback) callback();
        })
        .catch((e) => {
          // eslint-disable-next-line no-console
          console.log(e);
          setData({ loading: false })
        });
    }, [getComponent, getInjects]);

    const { Cmp, loading, injects } = data;

    if (loading) {
      return (
        <Spin wrapperClassName="c7ntest-Loading">
          <div style={{ width: '100%', height: '100%' }} />
        </Spin>
      );
    }
    return Cmp ? <Cmp {...Object.assign({}, extProps, props, ...injects)} /> : null;
  };

  AsyncRoute.displayName = 'AsyncRoute';

  return AsyncRoute;
}
