---
title: asyncRouter
nav:
title: utils

---

# asyncRouter

动态路由组件，异步加载组件

| 入参         | 介绍         | 类型                     |
| ------------ | ------------ | ------------------------ |
| getComponent | 异步加载组件 | Function:React.reactNode |
| getInjects   |              | Function                 |
| extProps     |              | Object                   |
| callback     |              | Function                 |

```jsx | pure
import React from 'react';
import { Route } from 'react-router-dom';
import { inject } from 'mobx-react';
import { asyncRouter } from '@zknow/utils';

const Model = asyncRouter(() => import('./routes/model'));

export default inject('AppState')(({ match }) => {
  return (
    <Route path={`${match.url}/site/model`} component={Model} />
  );
});


```

