import { STATIC_URL, LOCAL_EXTERNAL_ROUTE } from '../constants';
import corsImport from '../cors-import';

const manifestMap = new Map();

export default async (routeName) => {
  const manifest = manifestMap.get(routeName);
  if (manifest === undefined) {
    try {
      const basePath = LOCAL_EXTERNAL_ROUTE === routeName ? '/' : `${STATIC_URL}/${routeName}/`;
      await corsImport(`${basePath}importManifest.js`);
      manifestMap.set(routeName, true);
      return true;
    } catch (e) {
      manifestMap.set(routeName, false);
      return false;
    }
  }
  return manifest;
}
