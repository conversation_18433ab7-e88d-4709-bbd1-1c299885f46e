---
title: prompt
nav:
  title: util

---

# prompt

错误信息提示处理（choerodon-ui/pro/message 二次封装）

```js
import { prompt } from '@zknow/utils';
prompt(data.message)
```

| 入参      | 介绍               | 类型                                                     |
| --------- | ------------------ | -------------------------------------------------------- |
| content   | 错误信息           | string                                                   |
| type      | 报错类型           | 'success'\|'error'\|'info'\|'warning'\|'warn'\|'loading' |
| duration  |                    |                                                          |
| placement | 弹出位置           | leftBottom                                               |
| onClose   | 结束时触发函数钩子 | Function                                                 |




