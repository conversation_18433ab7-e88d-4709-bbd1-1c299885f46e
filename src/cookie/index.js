import Cookies from 'universal-cookie';
import { COOKIE_PREFIX } from '../constants';

const cookies = new Cookies();

const setCookie = (name, value, option) => cookies.set(COOKIE_PREFIX ? `${COOKIE_PREFIX}${name}` : name, value, option);

const getCookie = (name, option) => cookies.get(COOKIE_PREFIX ? `${COOKIE_PREFIX}${name}` : name, option);

const removeCookie = (name, option) => cookies.remove(COOKIE_PREFIX ? `${COOKIE_PREFIX}${name}` : name, option);

export {
  cookies as default,
  setCookie,
  getCookie,
  removeCookie,
};
