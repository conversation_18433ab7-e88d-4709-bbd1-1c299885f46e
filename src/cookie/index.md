---
title: cookie
nav:
  title: util

---

# cookie

添加，删除，查询 cookie

## getCookie

设置 cookie

```js
import {getCookie} from '@zknow/utils';
getCookie(name, option)
//  option => {path: '/',sameSite: 'none',secure: true,httpOnly: false}
```

| 入参   | 介绍             | 类型   |
| ------ | ---------------- | ------ |
| name   | cookie 名字      | string |
| option | Cookies 传入配置 | object |

```jsx
/**
 * title: 基本使用
 */
import {getCookie} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>getCookie('yqcloud_language')</div>
        <br />
    	<div style={{color:'#BCBCBC'}}>结果：{getCookie('yqcloud_language')}</div>
    </div>)
}
```

## setCookie

设置 cookie

```js
import {setCookie} from '@zknow/utils';
setCookie(name,value,option)
```

| 入参   | 介绍             | 类型   |
| ------ | ---------------- | ------ |
| name   | cookie 名字      | string |
| value  | 设置值           | string |
| option | cookies 传入配置 | object |

```jsx
/**
 * title: 基本使用
 */
import {setCookie,getCookie} from '@zknow/utils';
import {useState} from 'react';
export default ()=>{
    const [fup,setFup] = useState(false)
    return (
        <div>
        <div>setCookie('yqcloud_language','en_US')</div>
            <br />
        <button onClick={()=>{
                        setFup(!fup)
                        setCookie('yqcloud_language','en_US')
                    }}>设置en_US</button>
        <button onClick={()=>{
                        setFup(!fup)
                        setCookie('yqcloud_language','zh_CN')
                    }}>设置zh_CN</button>
        <br /><br />
    	<div style={{color:'#BCBCBC'}}>结果：{getCookie('yqcloud_language')}</div>
    </div>
)}
```

## removeCookie

删除 cookie

```js
import {removeCookie} from '@zknow/utils';
removeCookie(name,option)
```

| 入参   | 介绍        | 类型   |
| ------ | ----------- | ------ |
| name   | cookie 名字 | string |
| option | cookies 传入配置 | object  |



