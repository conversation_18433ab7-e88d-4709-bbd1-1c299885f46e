import axios from 'axios';
import isArray from 'lodash/isArray';
import isString from 'lodash/isString';
import React, { useEffect, useState } from 'react';
import { IntlProvider as ReactIntlProvider } from 'react-intl';

const cache = window.intlCache || (window.intlCache = new Map());

export default function formatterCollections({ code = '' } = {}) {
  return (Component) => {
    const IntlProvider = (props) => {
      const language =
        props?.AppState?.currentLanguage ||
        window?.__yqcloudStores__?.AppState?.currentLanguage ||
        'zh_CN';
      const tenantId =
        props?.AppState?.currentMenuType?.tenantId ||
        window?.__yqcloudStores__?.AppState?.currentMenuType?.tenantId ||
        0;
      const [data, setData] = useState();
      let currentLanguage = null;

      useEffect(() => {
        if (language && currentLanguage !== language) {
          currentLanguage = language;
          let currentCacheLang;
          if (cache.has(language)) {
            currentCacheLang = cache.get(language);
          } else {
            currentCacheLang = {};
            cache.set(language, currentCacheLang);
          }
          let promptKey = '';
          const multipleCode = [];
          if (isString(code)) {
            if (
              !Object.keys(currentCacheLang).some((key) => key.startsWith(code))
            ) {
              promptKey = code;
            }
          } else if (isArray(code)) {
            code.forEach((c) => {
              if (
                !Object.keys(currentCacheLang).some((key) => key.startsWith(c))
              ) {
                multipleCode.push(c);
              }
            });
            promptKey = multipleCode.join(',');
          }

          if (promptKey) {
            const queryLocaleData = async () => {
              try {
                const response = await axios.get(
                  `/hpfm/v1/${tenantId}/prompt/${language}?promptKey=${promptKey}`,
                );
                if (!response?.failed) {
                  cache.set(language, { ...currentCacheLang, ...response });
                  setData({ ...currentCacheLang, ...response });
                } else {
                  setData({ ...currentCacheLang });
                }
              } catch (error) {
                setData({ ...currentCacheLang });
                console.error('请求失败:', error);
              }
            };
            queryLocaleData();
          } else {
            setData(currentCacheLang);
          }
        }
      }, [language]);

      return data ? (
        <ReactIntlProvider
          {...props}
          locale={language.replace('_', '-')}
          messages={data}
        >
          <Component {...props} />
        </ReactIntlProvider>
      ) : null;
    };

    return IntlProvider;
  };
}
