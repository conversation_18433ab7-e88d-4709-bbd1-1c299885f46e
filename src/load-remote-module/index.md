---
title: loadRemoteModule
nav:
  title: util

---

# loadRemoteModule

Module Federation 2.0 动态远程模块加载器，用于加载远程微前端应用的组件或模块。

## 基础用法

```js
import { loadRemoteModule } from '@zknow/utils';

// 加载远程组件
const RemoteComponent = React.lazy(() => 
  loadRemoteModule('remote-app', './Component')
);

// 加载远程模块
const remoteModule = await loadRemoteModule('shared-components', './utils');
```

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| remoteName | 远程应用名称 | string | - |
| modulePath | 模块路径 | string | './' |

## 返回值

返回一个 Promise，resolve 时包含加载的模块。如果加载失败，会返回一个错误组件。

## 示例

### 加载远程组件

```jsx
import React, { Suspense } from 'react';
import { loadRemoteModule } from '@zknow/utils';

const RemoteButton = React.lazy(() => 
  loadRemoteModule('shared-components', './Button')
);

function App() {
  return (
    <div>
      <Suspense fallback={<div>加载中...</div>}>
        <RemoteButton type="primary">远程按钮</RemoteButton>
      </Suspense>
    </div>
  );
}
```

### 加载远程工具函数

```js
import { loadRemoteModule } from '@zknow/utils';

async function useRemoteUtils() {
  try {
    const utils = await loadRemoteModule('shared-utils', './helpers');
    return utils;
  } catch (error) {
    console.error('加载远程工具失败:', error);
    return null;
  }
}
```

## 特性

- ✅ 支持 Module Federation 2.0
- ✅ 自动错误处理和降级
- ✅ 兼容 webpack 5 模块联邦 API
- ✅ 提供错误边界组件
- ✅ TypeScript 支持
