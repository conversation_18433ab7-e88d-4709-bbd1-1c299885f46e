/**
 * Module Federation 2.0 动态远程模块加载器
 * 提供增强的远程模块加载功能
 */

/**
 * 动态加载远程模块
 * @param {string} remoteName - 远程模块名称
 * @param {string} modulePath - 模块路径
 * @returns {Promise<any>} 加载的模块
 */
export default async function loadRemoteModule(remoteName, modulePath = './') {
  try {
    // 检查是否支持 webpack 5 的模块联邦 API
    if (typeof __webpack_require__ !== 'undefined' && __webpack_require__.federation) {
      // 使用 webpack 5 的模块联邦 API
      const module = await __webpack_require__.federation.loadRemote(`${remoteName}${modulePath}`);
      return module;
    }
    
    // 降级到传统的动态导入
    const module = await import(/* webpackIgnore: true */ `${remoteName}${modulePath}`);
    return module;
  } catch (error) {
    console.error(`[MF Runtime] Failed to load remote module ${remoteName}${modulePath}:`, error);
    
    // 返回一个错误边界组件
    return {
      default: () => {
        if (typeof React !== 'undefined') {
          return React.createElement('div', {
            style: {
              padding: '20px',
              border: '1px solid #ff4d4f',
              borderRadius: '4px',
              backgroundColor: '#fff2f0',
              color: '#ff4d4f',
              textAlign: 'center',
            }
          }, `模块加载失败: ${remoteName}${modulePath}`);
        }
        return null;
      }
    };
  }
}
