---
title: randomString
nav:
  title: util

---

# randomString

生成指定长度的随机字符串

```js
import { randomString } from '@zknow/utils';
randomString()
```

| 入参 | 介绍               | 类型   |
| ---- | ------------------ | ------ |
| len  | 生成位数，默认32位 | number |

```jsx
/**
 * title: 基本使用
 */
import {randomString} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>randomString()</div>
        <br />
    	<div style={{color:'#BCBCBC'}}>结果：{randomString()}</div>
    </div>)
}
```

