/**
 * 过滤掉对象值为 undefined 和 空字符串 和 空数组 的属性
 * 并且为查询字段添加前缀 search_
 * <AUTHOR> <<EMAIL>>
 * @param {Object} obj: 数据源
 * @param {Array} filterList: 不需要前缀的字段
 * @returns {Object} 处理后的查询参数
 */
export default function getQueryParams(obj, filterList) {
  const queryPrefix = 'search_';
  const result = {};
  if (obj && Object.keys(obj).length >= 1) {
    Object.keys(obj).forEach((key) => {
      if (key && obj[key] !== undefined && obj[key] !== '' && obj[key] !== null) {
        // 如果查询的条件不为空
        if (Array.isArray(obj[key]) && obj[key].length === 0) {
          return;
        }
        if (Array.isArray(filterList) && filterList.length > 0 && filterList.includes(key)) {
          result[key] = obj[key];
        } else {
          result[`${queryPrefix}${key}`] = obj[key];
        }

      }
    });
  }
  return result; // 返回查询条件
}
