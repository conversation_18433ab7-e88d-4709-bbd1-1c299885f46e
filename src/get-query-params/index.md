---
title: getQueryParams 
nav:
  title: util

---

# getQueryParams 

因为表格搜索需要支持模糊查询，需要带上search_前缀。所以所有query的data需要使用getQuerryParams处理下。

如果需要排除的的字段，请设置数组。

```js
import { getQueryParams } from '@zknow/utils';
getQuerryParams(data, ['xxx', 'xxx']);
```

| 入参       | 介绍             | 类型     |
| ---------- | ---------------- | -------- |
| obj        | 数据源           | object   |
| filterList | 不需要前缀的字段 | string[] |



