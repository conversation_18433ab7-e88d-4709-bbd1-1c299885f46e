/**
 * Module Federation 2.0 类型定义
 * 提供更好的 TypeScript 支持和类型提示
 */

declare module '@module-federation/enhanced/webpack' {
  export interface ModuleFederationPluginOptions {
    name: string;
    filename?: string;
    exposes?: Record<string, string>;
    remotes?: Record<string, string>;
    shared?: Record<string, SharedConfig>;
    manifest?: boolean;
    runtimePlugins?: string[];
    experiments?: {
      federationRuntime?: string;
    };
    dev?: {
      port?: number;
      host?: string;
    };
  }

  export interface SharedConfig {
    singleton?: boolean;
    requiredVersion?: string | false;
    eager?: boolean;
    strictVersion?: boolean;
    version?: string;
  }

  export class ModuleFederationPlugin {
    constructor(options: ModuleFederationPluginOptions);
  }
}

declare module '@module-federation/enhanced/runtime' {
  export interface RuntimeOptions {
    name: string;
    remotes?: Array<{
      name: string;
      entry: string;
    }>;
  }

  export function init(options: RuntimeOptions): Promise<void>;
  export function loadRemote(name: string): Promise<any>;
  export function loadShare(name: string): Promise<any>;
}

// 全局类型扩展
declare global {
  interface Window {
    __FEDERATION__?: {
      __GLOBAL_LOADING_REMOTE_ENTRY__?: Record<string, Promise<any>>;
      __INSTANCES__?: Array<{
        name: string;
        version: string;
      }>;
    };
  }
}

// 模块联邦远程模块类型
declare module 'mf/*' {
  const component: React.ComponentType<any>;
  export default component;
}

// 项目特定的模块联邦类型
declare module '@zknow/*' {
  const module: any;
  export default module;
}

declare module '@choerodon/*' {
  const module: any;
  export default module;
}

declare module '@yqcloud/*' {
  const module: any;
  export default module;
}

export {};
