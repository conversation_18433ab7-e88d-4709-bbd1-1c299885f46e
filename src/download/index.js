import queryString from 'query-string';
import isObject from 'lodash/isObject';
import isArrayLike from 'lodash/isArrayLike';

let iframe;

export default function download(props) {
  const { url, method = 'get', data, target = '_export_window' } = props;
  if (target === '_export_window' && !iframe) {
    iframe = document.getElementById('_export_window');
    if (!iframe) {
      iframe = document.createElement('iframe');
      iframe.id = '_export_window';
      iframe.name = '_export_window';
      iframe.style.cssText = 'position:absolute;left:-10000px;top:-10000px;width:1px;height:1px;display:none';
      document.body.appendChild(iframe);
    }
  }
  if (method === 'get') {
    const parsedUrl = data ? `${url}${url.indexOf('?') === -1 ? '?' : '&'}${queryString.stringify(data)}` : url;
    if (target === '_export_window') {
      iframe.src = parsedUrl;
    } else {
      window.open(parsedUrl, target);
    }
  } else {
    const form = document.createElement('form');
    form.target = target;
    form.method = method;
    form.action = url;
    if (data) {
      Object.keys(data).forEach((key) => {
        const value = data[key];
        if (value) {
          const s = document.createElement('input');
          s.id = key;
          s.type = 'hidden';
          s.name = key;
          s.value = isObject(value) || isArrayLike(value) ? JSON.stringify(value) : value;
          form.appendChild(s);
        }
      });
    }
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }
}
