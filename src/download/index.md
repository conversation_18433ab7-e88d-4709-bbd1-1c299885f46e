---
title: download
nav:
  title: util

---

# download

下载组件，例如itsm的批量下载。

| 入参   | 介绍     | 类型             |
| ------ | -------- | ---------------- |
| url    | 下载地址 | string           |
| method | 请求方式 | get\|put\|push   |
| data   | 请求参数 | object           |
| target |          | '_export_window' |

```jsx | pure
import { download } from '@zknow/utils';

download({
   url: `${apiHost}/hfle/yqc/v1/${tenantId}/files/batch-download?access_token=${accessToken}&business_object_code=${viewDataSet?.current?.get('businessObjectCode')}`,
   method: 'post',
   data: { ids: data },
});
```

