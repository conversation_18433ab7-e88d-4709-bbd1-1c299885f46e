---
title: checkRemoteAvailability
nav:
  title: util

---

# checkRemoteAvailability

检查远程模块是否可用，用于在加载远程组件前验证服务可用性。

## 基础用法

```js
import { checkRemoteAvailability } from '@zknow/utils';

// 检查远程服务是否可用
const isAvailable = await checkRemoteAvailability('remote-app');
if (isAvailable) {
  // 加载远程组件
} else {
  // 显示降级内容
}
```

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| remoteName | 远程应用名称 | string | - |

## 返回值

返回一个 Promise<boolean>，表示远程模块是否可用。

## 示例

### 条件加载远程组件

```jsx
import React, { useState, useEffect } from 'react';
import { checkRemoteAvailability, loadRemoteModule } from '@zknow/utils';

function ConditionalRemoteComponent() {
  const [isAvailable, setIsAvailable] = useState(null);
  const [RemoteComponent, setRemoteComponent] = useState(null);

  useEffect(() => {
    const checkAndLoad = async () => {
      const available = await checkRemoteAvailability('shared-components');
      setIsAvailable(available);
      
      if (available) {
        const Component = React.lazy(() => 
          loadRemoteModule('shared-components', './Button')
        );
        setRemoteComponent(() => Component);
      }
    };
    
    checkAndLoad();
  }, []);

  if (isAvailable === null) {
    return <div>检查服务中...</div>;
  }

  if (!isAvailable) {
    return <div>远程服务不可用</div>;
  }

  return (
    <React.Suspense fallback={<div>加载中...</div>}>
      <RemoteComponent />
    </React.Suspense>
  );
}
```

### 批量检查多个远程服务

```js
import { checkRemoteAvailability } from '@zknow/utils';

async function checkMultipleRemotes() {
  const remotes = ['app1', 'app2', 'app3'];
  const results = await Promise.all(
    remotes.map(async (remote) => ({
      name: remote,
      available: await checkRemoteAvailability(remote),
    }))
  );
  
  console.log('远程服务状态:', results);
  return results;
}
```
