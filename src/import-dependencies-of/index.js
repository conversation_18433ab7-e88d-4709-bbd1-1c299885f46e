import corsImport from '../cors-import';
import getInConcurrency from '../external/getInConcurrency';
import getChunkPath from '../external/getChunkPath';
import getChunkDependencies from '../external/getChunkDependencies';


export default (basePath, nameSpace, module) => {
  if (!window.entryManifest) return;
  if (!nameSpace) return;
  if (!window.entryManifest[nameSpace]) return;

  return getInConcurrency(
    getChunkDependencies(basePath, nameSpace, module),
    corsImport,
  ).then(() => getChunkPath(basePath, nameSpace, module));
};
