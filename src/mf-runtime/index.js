/**
 * Module Federation 2.0 运行时管理
 * 提供运行时初始化、缓存管理、错误处理等功能
 */

/**
 * 获取已加载的远程模块信息
 * @returns {Array} 远程模块信息列表
 */
export function getLoadedRemotes() {
  if (typeof window !== 'undefined' && window.__FEDERATION__) {
    const federation = window.__FEDERATION__;
    if (federation.__INSTANCES__) {
      return federation.__INSTANCES__.map(instance => ({
        name: instance.name,
        version: instance.version || 'unknown',
      }));
    }
  }
  return [];
}

/**
 * 清理模块联邦缓存
 */
export function clearFederationCache() {
  if (typeof window !== 'undefined' && window.__FEDERATION__) {
    window.__FEDERATION__.__GLOBAL_LOADING_REMOTE_ENTRY__ = {};
  }
}

/**
 * 设置模块联邦错误处理
 * @param {boolean} debug - 是否开启调试模式
 */
export function setupMFErrorHandling(debug = false) {
  if (typeof window === 'undefined') return;

  // 监听模块联邦相关的错误
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && 
        event.reason.message.includes('Loading script failed')) {
      if (debug) {
        console.warn('[MF Runtime] Remote module loading failed:', event.reason);
      }
      // 可以在这里添加重试逻辑或降级处理
    }
  });

  // 监听网络错误
  window.addEventListener('error', (event) => {
    if (event.target && event.target.tagName === 'SCRIPT') {
      if (debug) {
        console.warn('[MF Runtime] Script loading error:', event.target.src);
      }
    }
  });
}

/**
 * 初始化模块联邦运行时
 * @param {Object} options - 初始化选项
 */
export function initMFRuntime(options = {}) {
  const { debug = false } = options;
  
  // 设置错误处理
  setupMFErrorHandling(debug);
  
  if (debug) {
    console.log('[MF Runtime] Module Federation 2.0 runtime initialized');
  }
}

export default {
  getLoadedRemotes,
  clearFederationCache,
  setupMFErrorHandling,
  initMFRuntime,
};
