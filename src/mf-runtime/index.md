---
title: mfRuntime
nav:
  title: util

---

# mfRuntime

Module Federation 2.0 运行时管理工具，提供运行时初始化、缓存管理、错误处理等功能。

## 基础用法

```js
import { initMFRuntime, getLoadedRemotes, clearFederationCache } from '@zknow/utils';

// 初始化运行时
initMFRuntime({ debug: true });

// 获取已加载的远程模块
const remotes = getLoadedRemotes();

// 清理缓存
clearFederationCache();
```

## API

### initMFRuntime(options)

初始化 Module Federation 运行时。

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| options.debug | 是否开启调试模式 | boolean | false |

### getLoadedRemotes()

获取已加载的远程模块信息。

**返回值：** Array<{name: string, version: string}>

### clearFederationCache()

清理模块联邦缓存。

### setupMFErrorHandling(debug)

设置模块联邦错误处理。

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| debug | 是否开启调试模式 | boolean | false |

## 示例

### 应用启动时初始化

```js
// 在应用入口文件中
import { initMFRuntime } from '@zknow/utils';

// 初始化 MF 运行时
initMFRuntime({
  debug: process.env.NODE_ENV === 'development',
});
```

### 监控远程模块状态

```js
import { getLoadedRemotes } from '@zknow/utils';

function RemoteModuleMonitor() {
  const [remotes, setRemotes] = useState([]);

  useEffect(() => {
    const updateRemotes = () => {
      setRemotes(getLoadedRemotes());
    };

    // 定期更新远程模块状态
    const interval = setInterval(updateRemotes, 5000);
    updateRemotes();

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <h3>已加载的远程模块:</h3>
      {remotes.map(remote => (
        <div key={remote.name}>
          {remote.name} - {remote.version}
        </div>
      ))}
    </div>
  );
}
```

### 缓存管理

```js
import { clearFederationCache } from '@zknow/utils';

// 在需要时清理缓存
function handleClearCache() {
  clearFederationCache();
  console.log('模块联邦缓存已清理');
}

// 在组件卸载时清理缓存
useEffect(() => {
  return () => {
    clearFederationCache();
  };
}, []);
```
