import { getAccessToken, axios as globalAxios } from '@zknow/utils';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import React, { useEffect, useImperativeHandle, useState } from 'react';
// 使用 webOffice JSSDK 在预览页面很多配置不生效
// https://wps.zknow.com/open#/docs/client/onlinepreview-jssdk
import OpenSDK from './assets/open-jssdk-v1.0.0.es';
import './index.less';

const typeList = {
  w: ['doc', 'dot', 'wps', 'wpt', 'docx', 'dotx', 'docm', 'dotm', 'rtf'],
  s: ['xls', 'xlt', 'et', 'xlsx', 'xltx', 'csv', 'xlsm', 'xltm'],
  p: [
    'ppt',
    'pptx',
    'pptm',
    'ppsx',
    'ppsm',
    'pps',
    'potx',
    'potm',
    'dpt',
    'dps',
  ],
  f: ['pdf'],
};

const Index = inject('AppState')(
  observer((props) => {
    const {
      fileKey,
      tenantId,
      sourceId,
      userId,
      AppState: {
        userInfo: { id: appStateUserId },
      },
      style,
      fileSourceType,
      wpsEventHandle = () => {},
      isEdit = false,
      handlerEditResult,
      importFrom = window.location.host.includes('choerodon')
        ? 'c7n'
        : 'yqcloud',
      overwriteConfig = {},
      cRef = { current: undefined },
      isPublic,
    } = props;

    const axios = props.axios || globalAxios;

    const [jssdk, setJssdk] = useState(null);

    useImperativeHandle(cRef, () => ({
      jssdk,
    }));

    const initWps = async ({ url }) => {
      const insidejssdk = await OpenSDK.config({
        // wpsPreview=0 确保 wps 图片不压缩，保证图片清晰度
        url: `${url}&wpsPreview=0`, // 该地址需要后端提供，https://wwo.wps.cn/office/p/xxx
        refreshToken: () => {
          return Promise.resolve({
            token: getAccessToken()?.split(' ')[1], // 必需：你需要设置的 toekn
            timeout: 10 * 60 * 1000, //  必需：token 超时时间，以 10 分钟示例
          });
        },
        mount: document.querySelector(
          importFrom === 'c7n' ? '#c7ncd-center-wps' : '#knowledge-center-wps',
        ),
        setToken: {
          token: getAccessToken()?.split(' ')[1], // 根据自身的业务需求，通过异步请求或者模板输出的方式，取得 token
          timeout: 10 * 60 * 1000, // token 超时时间，可配合 refreshToken 配置函数使用，在超时前调用 refreshToken 重新刷新 token
        },
        ...overwriteConfig,
      });

      await insidejssdk?.ready();
      const app = insidejssdk?.Application;
      const help = await app?.CommandBars('FloatQuickHelp');
      if (help) {
        help.Visible = false;
      }

      wpsEventHandle(insidejssdk);
      setJssdk(insidejssdk);
    };

    const init = async () => {
      const data = {
        fileKey,
        tenantId,
        token: getAccessToken()?.split(' ')[1],
        sourceId,
      };

      if (!isPublic) {
        data.userId = userId || appStateUserId;
      }

      if (isEdit) {
        const fileType = fileKey.split('@').pop().split('.').pop();
        const key = Object.keys(typeList).find((type) =>
          typeList[type].includes(fileType.toLowerCase()),
        );
        data.type = key;
      }

      if (fileSourceType) {
        data.fileSourceType = fileSourceType;
      }

      const res = await axios({
        url: isEdit
          ? 'knowledge/v1/wps/new/view/info'
          : 'knowledge/v1/wps/preview',
        method: 'post',
        data,
      });

      if (isEdit) {
        if (res?.result === 0) {
          initWps({ url: res?.url });
        } else if (handlerEditResult) {
          handlerEditResult(res);
        } else {
          // eslint-disable-next-line
          console.error('wps编辑出错');
        }
      } else {
        initWps({
          url: isPublic
            ? res.url.replace(
                '/files/download-by-key',
                '/files/public/download-by-key',
              )
            : res?.url,
        });
      }
    };

    const refreshNode = () => {
      const parent = document.querySelector('.c7ncd-center-wps-parent');
      const createNode = () => {
        const div = document.createElement('div');
        div.id = 'c7ncd-center-wps';
        parent?.appendChild(div);
      };
      if (parent?.innerHTML) {
        parent.innerHTML = '';
      }
      createNode();
    };

    useEffect(() => {
      const head = document.querySelector('head');
      let targetMeta = document.querySelector('meta[content="no-referrer"]');
      if (!targetMeta) {
        targetMeta = document.createElement('meta');
        targetMeta.setAttribute('name', 'referrer');
        targetMeta.setAttribute('content', 'no-referrer');
        head.appendChild(targetMeta);
      }
      return () => {
        if (head.contains(targetMeta)) {
          head.removeChild(targetMeta);
        }
      };
    }, []);

    useEffect(() => {
      if (importFrom === 'c7n') {
        refreshNode();
      }
      init();
    }, [fileKey, isEdit]);

    return importFrom === 'c7n' ? (
      <div className="c7ncd-center-wps-parent" style={style || {}}>
        <div id="c7ncd-center-wps" />
      </div>
    ) : (
      <div id="knowledge-center-wps" style={style || {}} />
    );
  }),
);

export default Index;
