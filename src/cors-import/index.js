import loadjs from 'loadjs';

const map = new Map();

export default (url) => {
  if (!url) {
    return new Promise((resolve, reject) =>
      reject(new Error('no url in corsImport')),
    );
  }
  if (loadjs.isDefined(url)) {
    const pending = map.get(url);
    if (pending) {
      return pending;
    }
    return Promise.resolve();
  }

  const promise = new Promise((resolve, reject) => {
    const myResolve = (result) => {
      map.delete(url);
      resolve(result);
    };
    const myReject = (error) => {
      map.delete(url);
      reject(error);
    };

    loadjs(url, url, {
      success: myResolve,
      error: myReject,
    });
  });

  map.set(url, promise);

  return promise;
};
