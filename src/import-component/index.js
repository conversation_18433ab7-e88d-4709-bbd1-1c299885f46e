import importDependenciesOf from '../import-dependencies-of';
import { STATIC_URL, LOCAL_EXTERNAL_ROUTE } from '../constants';

const mainMap = new Map();

export default async (routeName, module) => {
  const key = `${routeName}_${module}`
  const component = mainMap.get(key);
  if (component === undefined) {
    try {
      const basePath = LOCAL_EXTERNAL_ROUTE === routeName ? '/' : `${STATIC_URL}/${routeName}/`;
      const url = await importDependenciesOf(basePath, routeName, module);
      mainMap.set(key, url);
      return url;
    } catch (e) {
      mainMap.set(key, false);
      return false;
    }
  }
  return component;
}
