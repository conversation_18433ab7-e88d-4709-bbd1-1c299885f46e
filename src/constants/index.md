---
title: constants
nav:
title: util

---

# Constants

常量集合，例如API_HOST、TOKEN_TYPE、AUTH_HOST、LOCAL等

```jsx | pure
import {Constants} from '@zknow/utils';
Constants.TOKEN_TYPE
```

```
// 常量
ZH_CN,
EN_US,
CODE_REGEX,
LOCAL,
CLIENT_ID,
API_HOST,
COOKIE_SERVER,
FILE_SERVER,
WEBSOCKET_SERVER,
APIM_GATEWAY,
EMAIL_BLOCK_LIST,
HEADER_TITLE_NAME,
BUZZ_WEBSOCKET_URL,
PREFIX_CLS,
ACCESS_TOKEN,
TOKEN_TYPE,
ACCESS_DOMAIN,
STRING_DEVIDER,
NODE_ENV,
AUTH_HOST,
AUTH_URL,
MENU_THEME,
SERVICES_CONFIG,
TYPE,
RESOURCES_LEVEL,
UI_CONFIGURE,
OUTWARD,
LOCAL_EXTERNAL_ROUTE,
MULTIPLE_FORM_TOP,
STATIC_URL,
```

```jsx
/**
 * title: 基本使用
 */
import {Constants} from '@zknow/utils';
console.log(Constants)
export default ()=>{
    return (<div>
        <span>Constants.TOKEN_TYPE:</span>
    	<span>{Constants.TOKEN_TYPE}</span>
    </div>)
}
```

