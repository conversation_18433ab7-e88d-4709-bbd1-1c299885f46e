import getEnv from '../get-env';
import cookie from '../cookie';

function getEnvByHost() {
  const { hostname } = window.location || {};
  const defaultEnv = getEnv('NODE_ENV', '');
  const env = hostname?.split('.')[0];
  if (env && env !== 'localhost' && env !== 'apps' && !Number(env)) {
    return env;
  }
  return defaultEnv;
}

export const LOCAL = JSON.parse(getEnv('LOCAL', '') || 'true');
export const CLIENT_ID = getEnv('CLIENT_ID', '');
export const API_HOST = getEnv('API_HOST', '');
export const STATIC_URL = getEnv('STATIC_URL', '');
export const COOKIE_SERVER = getEnv('COOKIE_SERVER', '');
export const FILE_SERVER = getEnv('FILE_SERVER', `${''}`);
export const WEBSOCKET_SERVER = getEnv('WEBSOCKET_SERVER', `${''}`);
export const APIM_GATEWAY = getEnv('APIM_GATEWAY', '');
export const EMAIL_BLOCK_LIST = getEnv('EMAIL_BLACK_LIST', '');
export const HEADER_TITLE_NAME = `${getEnv('HEADER_TITLE_NAME', '') || getEnv('TITLE_NAME', '') || 'YQCloud'}`;
export const BUZZ_WEBSOCKET_URL = getEnv('BUZZ_WEBSOCKET_URL', '') || 'ws://buzz.choerodon.staging.saas.hand-china.com';
export const LOCAL_EXTERNAL_ROUTE = getEnv('LOCAL_EXTERNAL_ROUTE', '');
export const NODE_ENV = getEnvByHost();
export const MENU_THEME = getEnv('MENU_THEME', '');
export const SERVICES_CONFIG = getEnv('SERVICES_CONFIG', '');
export const TYPE = getEnv('TYPE', '');
export const RESOURCES_LEVEL = getEnv('RESOURCES_LEVEL', '');
export const UI_CONFIGURE = getEnv('UI_CONFIGURE', '');
export const OUTWARD = getEnv('OUTWARD', '');
export const BASE_DOMAIN = getEnv('BASE_DOMAIN', '');
export const OAUTH_HEADER = getEnv('OAUTH_HEADER', '');
export const COOKIE_PREFIX = getEnv('COOKIE_PREFIX', '');

export const ZH_CN = {
  add: '添加',
  new: '新建',
  'validate.message.code': '编码只能由大写字母，数字和下划线构成',
};

export const EN_US = {
  add: 'Add',
  new: 'New',
  'validate.message.code': 'The code can only be made up of capital letters, numbers and underscores',
};
export const CODE_REGEX = /^[A-Z0-9_]+$/;
export const PREFIX_CLS = 'c7n';
const disableSdkTokenShare = cookie.get('disable_sdk_token_share');
const isFeedback = window.location.hash?.includes('/lc/feedback');
export const ACCESS_TOKEN = isFeedback && disableSdkTokenShare === 'true' ? 'feedback_access_token' : (OAUTH_HEADER || 'access_token');
export const TOKEN_TYPE = 'token_type';
export const ACCESS_DOMAIN = 'domain';
export const STRING_DEVIDER = '__@.@__';

const DOMAIN = window.location.hostname?.split('.')[0];
let AUTH_HOST_URL = `${API_HOST.replace('://api', `://${DOMAIN}`)}/oauth`;

// 针对于AUTH_HOST不是以api.开头的场景
if (BASE_DOMAIN) {
  let dot = '.';
  if (BASE_DOMAIN.indexOf('.') === 0) {
    dot = '';
  }
  AUTH_HOST_URL = `//${DOMAIN}${dot}${BASE_DOMAIN}/oauth`;
}
export const AUTH_HOST = AUTH_HOST_URL;
export const AUTH_URL = `${AUTH_HOST_URL}/oauth/authorize?response_type=token&client_id=${DOMAIN}&state=`;

// 样式类 ClassName
// Form
export const MULTIPLE_FORM_TOP = ' multiple-form-top '

export default {
  ZH_CN,
  EN_US,
  CODE_REGEX,
  LOCAL,
  CLIENT_ID,
  API_HOST,
  COOKIE_SERVER,
  FILE_SERVER,
  WEBSOCKET_SERVER,
  APIM_GATEWAY,
  EMAIL_BLOCK_LIST,
  HEADER_TITLE_NAME,
  BUZZ_WEBSOCKET_URL,
  PREFIX_CLS,
  ACCESS_TOKEN,
  TOKEN_TYPE,
  ACCESS_DOMAIN,
  STRING_DEVIDER,
  NODE_ENV,
  AUTH_HOST,
  AUTH_URL,
  MENU_THEME,
  SERVICES_CONFIG,
  TYPE,
  RESOURCES_LEVEL,
  UI_CONFIGURE,
  OUTWARD,
  LOCAL_EXTERNAL_ROUTE,
  MULTIPLE_FORM_TOP,
  STATIC_URL,
};

