---
title: color
nav:
  title: util

---

# color

颜色计算方法合集

例如：计算颜色深浅，颜色透明度转化等方法.

## colorPalette

处理颜色饱和度。

```jsx | pure
import {color} from '@zknow/utils';
color.colorPalette(color, index)
```



| 入参  | 介绍 | 类型   |
| ----- | ---- | ------ |
| color | 颜色 | string |
| opts  |      | number |

```jsx
/**
 * title: 基本使用
 */
import {color} from '@zknow/utils';

export default ()=>{
    return (<div>
        <span>#1387F4:</span>
    	<span style={{color:'#1387F4'}}>#1387F4</span>
        <br />
        <br />
        <div>color.colorPalette('#1387F4',2)</div>
    	<div style={{color:color.colorPalette('#1387F4',2)}}>#1387F4</div>
        <br />
        <div>color.colorPalette('#1387F4',6)</div>
    	<div style={{color:color.colorPalette('#1387F4',6)}}>#1387F4</div>
        <br />
        <div>color.colorPalette('#1387F4',8)</div>
    	<div style={{color:color.colorPalette('#1387F4',8)}}>#1387F4</div>
        <br />
    </div>)
}
```

## colorOpacityHSV

处理颜色饱和度。

```jsx | pure
import {color} from '@zknow/utils';
color.colorOpacityHSV(color, opacity)
```



| 入参    | 介绍 | 类型   |
| ------- | ---- | ------ |
| color   | 颜色 | string |
| opacity |      | number |

```jsx
/**
 * title: 基本使用
 */
import {color} from '@zknow/utils';

export default ()=>{
    return (<div>
        <span>#1387F4:</span>
    	<span style={{color:'#1387F4'}}>#1387F4</span>
        <br />
        <br />
        <div>color.colorOpacityHSV('#1387F4',0.2)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.2)}}>#1387F4</div>
        <br />
        <div>color.colorOpacityHSV('#1387F4',0.6)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.6)}}>#1387F4</div>
        <br />
        <div>color.colorOpacityHSV('#1387F4',0.8)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.8)}}>#1387F4</div>
        <br />
    </div>)
}
```

## colorOpacityRGB

处理颜色透明度。

```jsx | pure
import {color} from '@zknow/utils';
color.colorOpacityRGB(color, opacity)
```



| 入参    | 介绍 | 类型   |
| ------- | ---- | ------ |
| color   | 颜色 | string |
| opacity |      | number |

```jsx
/**
 * title: 基本使用
 */
import {color} from '@zknow/utils';

export default ()=>{
    return (<div>
        <span>#1387F4:</span>
    	<span style={{color:'#1387F4'}}>#1387F4</span>
        <br />
        <br />
        <div>color.colorOpacityRGB('#1387F4',0.2)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.2)}}>#1387F4</div>
        <br />
        <div>color.colorOpacityRGB('#1387F4',0.6)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.6)}}>#1387F4</div>
        <br />
        <div>color.colorOpacityRGB('#1387F4',0.8)</div>
    	<div style={{color:color.colorOpacityHSV('#1387F4',0.8)}}>#1387F4</div>
        <br />
    </div>)
}
```

## calColorShade

计算颜色深浅 深色返回true,浅色返回false

```jsx | pure
import {color} from '@zknow/utils';
color.calColorShade(colorValue)
```

| 入参       | 介绍 | 类型   |
| ---------- | ---- | ------ |
| colorValue | 颜色 | string |

```jsx
/**
 * title: 基本使用
 */
import {color} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>color.calColorShade('#d0d0d0')</div>
    	<div style={{color:"#d0d0d0"}}>{color.calColorShade('#d0d0d0').toString()}</div>
        <div>color.calColorShade('#090909')</div>
    	<div style={{color:"#090909"}}>{color.calColorShade('#090909').toString()}</div>
    </div>)
}
```

## getColorCorrespondingValue

根据名字返回颜色

```jsx | pure
import {color} from '@zknow/utils';
color.getColorCorrespondingValue(colorCode)
```

| 入参      | 介绍     | 类型   |
| --------- | -------- | ------ |
| colorCode | 颜色名字 | string |

```jsx
/**
 * title: 基本使用
 */
import {color} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>color.getColorCorrespondingValue('red')</div>
    	<div style={{color:color.getColorCorrespondingValue('red')}}>{color.getColorCorrespondingValue('red')}</div>
    </div>)
}
```

