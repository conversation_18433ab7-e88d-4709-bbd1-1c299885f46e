const colorCorrespondingValue = {
  'red': '#FABFBD', // 红色

  'orange': '#FFE7BA', // 橙色

  'yellow': '#FCE996', // 黄色
  'tangerineOrange': '#FFD97F', // 橘色

  'green': '#D9F7BE', // 绿色
  'grassGreen': '#C7F4CB', // 草绿色
  'turquoise': '#ACE3B6', // 青绿色

  'blue': '#BAE7FF', // 蓝色
  'lightBlue': '#C8E2FF', // 浅蓝
  'cyan': '#B5F5EC', // 青色

  'purple': '#EFDBFF', // 紫色
  'lilacColour': '#D6E4FF', // 浅紫色

  'grey': '#E5E6EB', // 灰色
}
const colorDefaultValue = {
  'red': '#F34C4B', // 红色

  'orange': '#FD7D23', // 橙色

  'yellow': '#FADC19', // 黄色
  'tangerineOrange': '#FFB400', // 橘色

  'green': '#1AB335', // 绿色
  'grassGreen': '#9FDB1D', // 草绿色
  'turquoise': '#1BD390', // 青绿色

  'blue': '#2979FF', // 蓝色
  'lightBlue': '#1DC8FF', // 浅蓝
  'cyan': '#14C9C9', // 青色

  'purple': '#7816FF', // 紫色
  'lilacColour': '#D91AD9', // 浅紫色

  'grey': '#C9CDD4', // 灰色
}


function getColorDefaultValue(colorCode){
  return colorDefaultValue[colorCode] || '#2979FF'
}
function getColorCorrespondingValue(colorCode) {
  return colorCorrespondingValue[colorCode] || '#2979FF';
}

export {
  getColorCorrespondingValue,
  getColorDefaultValue
};
