import { tinycolor } from './tinyColor';
import { getColorCorrespondingValue, getColorDefaultValue } from './statusColor';

const hueStep = 2;
const saturationStep = 16;
const saturationStep2 = 5;
const brightnessStep1 = 5;
const brightnessStep2 = 15;
const lightColorCount = 5;
const darkColorCount = 4;

const getHue = (hsv, i, isLight) => {
  let hue;
  if (hsv.h >= 60 && hsv.h <= 240) {
    hue = isLight ? hsv.h - hueStep * i : hsv.h + hueStep * i;
  } else {
    hue = isLight ? hsv.h + hueStep * i : hsv.h - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return Math.round(hue);
};

const getSaturation = (hsv, i, isLight) => {
  let saturation;
  if (isLight) {
    saturation = Math.round(hsv.s * 100) - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = Math.round(hsv.s * 100) + saturationStep;
  } else {
    saturation = Math.round(hsv.s * 100) + saturationStep2 * i;
  }
  if (saturation > 100) {
    saturation = 100;
  }
  if (isLight && i === lightColorCount && saturation > 10) {
    saturation = 10;
  }
  if (saturation < 6) {
    saturation = 6;
  }
  return Math.round(saturation);
};

const getValue = (hsv, i, isLight) => {
  if (isLight) {
    return Math.round(hsv.v * 100) + brightnessStep1 * i;
  }
  return Math.round(hsv.v * 100) - brightnessStep2 * i;
};

const colorPalette = (color, index) => {
  const isLight = index <= 6;
  const hsv = tinycolor(color).toHsv();
  const i = isLight ? lightColorCount + 1 - index : index - lightColorCount - 1;
  return tinycolor({
    h: getHue(hsv, i, isLight),
    s: getSaturation(hsv, i, isLight),
    v: getValue(hsv, i, isLight),
  }).toHexString();
};

const colorOpacityHSV = (color, opacity = 1) => {
  const hsv = tinycolor(color).toHsv();
  return tinycolor({
    h: hsv.h,
    s: hsv.s * 100 * opacity,
    v: hsv.v * 100 * (1 + opacity * 1.3),
  }).toHexString();
};

// 计算不透明颜色为透明颜色
const colorOpacityRGB = (color, opacity = 0.15) => {
  if (+opacity === 0) return `${color}00`;
  if (+opacity === 1) return `${color}ff`;
  const strA = color.slice(1, 3);
  const strB = color.slice(3, 5);
  const strC = color.slice(5, 7);
  let r = Number(`0x${strA}`);
  let g = Number(`0x${strB}`);
  let b = Number(`0x${strC}`);
  r = Math.floor(opacity * r + (1 - opacity) * 255);
  g = Math.floor(opacity * g + (1 - opacity) * 255);
  b = Math.floor(opacity * b + (1 - opacity) * 255);
  return `#${
    (`0${r.toString(16)}`).slice(-2)
  }${(`0${g.toString(16)}`).slice(-2)
  }${(`0${b.toString(16)}`).slice(-2)}`;
};

// 计算颜色深浅 深色返回true,浅色返回false
const calColorShade = (colorValue) => {
  if (!colorValue || !colorValue?.length) return true;
  function resBgColor(rgbArr) {
    return 0.213 * rgbArr[0] + 0.715 * rgbArr[1] + 0.072 * rgbArr[2] < 255 / 1.5; // 这个判断系数不是很准确
  }
  function hexToRgb(hex) {
    // hex 格式 #000000
    return `rgb(${parseInt(`0x${hex?.slice(1, 3)}`, 16)},${parseInt(`0x${hex?.slice(3, 5)}`, 16)},${parseInt(`0x${hex?.slice(5, 7)}`, 16)})`;
  }
  const color = /^(rgb|RGB)/.test(colorValue) ? colorValue : hexToRgb(colorValue);
  if (/^(rgb|RGB)/.test(color)) {
    const aColor = color.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',');
    return resBgColor(aColor);
  }
  return true;
};

export default { colorPalette, colorOpacityHSV, colorOpacityRGB, calColorShade, getColorCorrespondingValue, getColorDefaultValue };
