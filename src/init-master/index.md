---
title: initMaster
nav:
  title: util

---

# initMaster

给utils/store注入master数据，用于 root 中

| 入参   | 介绍                                | 类型 |
| ------ | ----------------------------------- | ---- |
| master | app-master/lib/index master入口文件 |      |

```jsx | pure
import React from 'react';
import { Route } from 'react-router-dom';
import { inject } from 'mobx-react';
import { asyncRouter } from '@zknow/utils';

const Model = asyncRouter(() => import('./routes/model'));

export default inject('AppState')(({ match }) => {
  return (
    <Route path={`${match.url}/site/model`} component={Model} />
  );
});


```

