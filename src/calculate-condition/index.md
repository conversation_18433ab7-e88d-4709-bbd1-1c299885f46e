---
title: calculateCondition
nav:
  title: util

---

# calculateCondition

前端低代码条件判断组件。（其他包也有用到该功能）

lowcode中也写了一份，这里建议迁移即可。后续都使用该组件，低代码那边删除该组件。

## calculateCondition

计算条件关系、

```jsx | pure
import {calculateCondition} from '@zknow/utils';

calculateCondition(parentKey, conditions, record, funcConfig)
```

| 入参       | 介绍                                                         | 类型     |
| ---------- | ------------------------------------------------------------ | -------- |
| parentKey  | 选填父级key，后边与filters[i].field相拼                      | string   |
| conditions | 必填数组条件组                                               | object[] |
| record     | 必填当前数据record                                           | record   |
| funcConfig | 表达式需求的上下文数据如 funcConfig.personId; funcConfig.person | object   |



## calculateConditions

输入多个条件组，计算条件关系

```jsx | pure
import {calculateCondition} from '@zknow/utils';

calculateConditions(parentKey,conditionMap,conditionIds,record,extraConditions,funcConfig)
```

