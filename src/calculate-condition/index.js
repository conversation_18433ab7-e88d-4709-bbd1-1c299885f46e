import { toJS } from 'mobx';
import moment from 'moment';

const INPUT_FIELDS = [
  'Input',
  'TextArea',
  'Password',
  'Email',
  'NumberField',
  'FloatNumber',
  'Currency',
  'Url',
  'Duration',
];
export const OPERATOR = {
  IS_ASSIGN_VARIABLE: 'is assign variable',
  IS: 'is',
  IS_NOT: 'is not',
  NULL: 'is null',
  NOT_NULL: 'is not null',
  IN_LIST: 'is in list',
  NOT_IN_LIST: 'is not in list',
  IS_NOT_EMPTY: 'is not empty',
  IS_EMPTY: 'is empty',

  IS_EARLY_THAN: 'is early than',
  IS_LATER_THAN: 'is later than',
  IS_NOT_EARLY_THAN: 'is not early than',
  IS_NOT_LATER_THAN: 'is not later than',

  STARTS_WITH: 'starts with',
  ENDS_WITH: 'ends with',
  EXISTS: 'exists',
  DOES_NOT_EXISTS: 'does not exist',
  CONTAINS: 'contains',
  DOES_NOT_CONTTAIN: 'does not contain',

  BETWEEN: 'between',
  IS_GREATER_THAN: 'is greater than',
  IS_SMALLER_THAN: 'is smaller than',
  IS_NOT_GREATER_THAN: 'is not greater than',
  IS_NOT_SMALLER_THAN: 'is not smaller than',

  IS_LIKE: 'is like',
  IS_STEP: 'is step',
  IS_NOTHING: 'is Nothing',

  IS_UPDATE: 'is update',
  IS_VARIABLE: 'is variable',
  IS_CURRENT_USER: 'is current user',
};
const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};
const MOMENT_FIELDS = ['DateTime', 'Date', 'Time'];
const MULTIPLE_FIELDS = ['MultipleSelect', 'SelectBox'];
export const RELATION = {
  AND: 'AND',
  OR: 'OR',
};

function isJSON(str, type = 'object') {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str);
      // eslint-disable-next-line valid-typeof
      if (typeof obj === type && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  return '';
}

// quill空值
const quillNullContent = [
  '[{"insert":"\\n"}]',
  '[{"insert":" \n"}]',
  '[{"insert":"\n"}]',
  '[{"insert":""}]',
  '[]',
  '{}',
  '',
  null,
  'null',
  undefined,
  '<p>​</p>',
  undefined,
];

export function getRichTextIsNull(content = '') {
  if (!content) return true;
  if (/* quill对象 */ isJSON(content)) {
    return quillNullContent.includes(content);
  }
  if (/* JSON.stringify 一个String类型的数据 */ isJSON(content, 'string')) {
    if (
      /* quill对象, 传过来的是JSON.stringify之后的数据 */ isJSON(
        JSON.parse(content),
      )
    ) {
      return quillNullContent.includes(JSON.parse(content));
    }
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = JSON.parse(content).substring(
      0,
      JSON.parse(content).indexOf('<p data-json='),
    );
    return quillNullContent.includes(data);
  }
  if (/* 纯字符串 */ content.indexOf('<p data-json=') > -1) {
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = content.substring(0, content.indexOf('<p data-json='));
    return quillNullContent.includes(data);
  }
  return false;
}

function compareText(type, text, curText) {
  if (!curText) return false;
  const index = curText.indexOf(text);
  if (index === -1) return false;
  const endIndex = index + text.length;
  return (
    (index === 0 && type === 'start') ||
    (endIndex === curText.length && type === 'end') ||
    (index !== -1 && type === 'contains')
  );
}

function compareTime(time, curTime) {
  if (!curTime) return false;
  const cur = new Date(curTime);
  if (typeof time === 'string' && time?.indexOf(',') !== -1) {
    const tArr = time.split(',').map((item) => new Date(item));
    return cur > tArr[0] && cur < tArr[1];
  }
  const t = new Date(time);
  // 当前时间比time早，返回true
  return cur < t;
}

function operatorIs(curValue, fieldValue, widgetType) {
  // 临时： CheckBox在Conditon中的配置类型是Select
  if (['MultipleSelect', 'Select', 'SelectBox', 'Tag'].includes(widgetType)) {
    const value = fieldValue?.split(',');
    return value?.every((item) => {
      if (typeof curValue?.split === 'function') {
        return curValue && curValue?.split(',').indexOf(item) !== -1;
      }
      if (typeof curValue?.find === 'function') {
        return curValue && curValue?.find((option) => option === item);
      }
      return curValue === fieldValue;
    });
  }
  if (widgetType === 'RichText' && curValue.length) {
    return fieldValue === curValue[0]?.insert?.replace('\n', '');
  }
  return curValue === fieldValue;
}

// 设计器中执行的js
function transformDesignExpression(expression) {
  return expression
    .replace(/\$GetCurrentAttribute/g, 'getValue')
    .replace(/\$GetCurrentPersonId/g, 'getCurrentPersonId')
    .replace(/\$GetCurrentPerson/g, 'getCurrentPerson')
    .replace(/\$GetCurrentTime/g, 'getCurrentTime');
}

export function executeExpression(fieldDto, record, funcConfig) {
  const { expression } = fieldDto;
  try {
    const config = `
        var personId = "${funcConfig?.personId}";
        var person = ${JSON.stringify(funcConfig?.person || {})};
      `;
    let functionExpression = config;
    const getCurrentPersonId = `function getCurrentPersonId(){
        return personId;
      }\n`;
    const getCurrentPerson = `function getCurrentPerson(){
        return person;
      }\n`;
    const getCurrentAttribute = `function getValue(code){
           return record && record.get(code);
      }\n`;
    const getFieldValue = `function getValue(code){
           return record && record.get(code);
      }`;
    const getCurrentTime = `function getCurrentTime(code){
        return new Date();
      }`;
    if (expression.includes('GetCurrentPersonId')) {
      functionExpression += getCurrentPersonId;
    }
    if (expression.includes('GetCurrentAttribute')) {
      functionExpression += getCurrentAttribute;
    }
    if (expression.includes('GetValue')) {
      functionExpression += getFieldValue;
    }
    if (expression.includes('GetCurrentPerson')) {
      functionExpression += getCurrentPerson;
    }
    if (expression.includes('GetCurrentTime')) {
      functionExpression += getCurrentTime;
    }
    // eslint-disable-next-line no-new-func
    const func = new Function(
      'record',
      functionExpression + transformDesignExpression(expression),
    );
    const defaultValue = func(record);
    return defaultValue;
  } catch (e) {
    return null;
  }
}
/**
 * 输入多个条件组，计算条件关系
 * @param parentKey 选填父级key，后边与filters[i].field相拼
 * @param conditions 必填数组条件组
 * @param record 必填当前数据record
 * @param funcConfig 表达式需求的上下文数据如 funcConfig.personId; funcConfig.person
 * @returns boolean
 * */
export function calculateCondition(parentKey, conditions, record, funcConfig) {
  let conditionFlag = true;
  if (conditions?.length) {
    conditions.map((conditionItem) => {
      const { condition, filters } = conditionItem;
      let filterFlag = true;
      if (filters && filters.length) {
        filters.map((filterItem) => {
          // 默认条件符合
          let newFilterFlag = true;
          const {
            condition: filterCondition,
            field,
            filter,
            fieldValue: originFieldValue,
            fieldLovValue,
            widgetType,
            fieldValueType,
          } = filterItem;
          let currentValue; //
          let actualValue; // （新）
          let fieldValue = originFieldValue;
          if (!originFieldValue && widgetType === 'MasterDetail') {
            try {
              const lovValue = JSON.parse(fieldLovValue);
              fieldValue = lovValue?.id || lovValue;
            } catch (e) {
              fieldValue = '';
            }
          }
          const realField = parentKey ? `${parentKey}.${field}` : field;
          if (INPUT_FIELDS.includes(widgetType)) {
            // 'Input','TextArea','Password','Email','NumberField','FloatNumber','Currency','Url','Duration',
            // 对于可输入类型的组件，不能在onChange触发时就立即进行条件判断，先取原始值
            // getPristineValue(fieldName) 根据字段名获取字段的原始值。
            currentValue =
              record.getPristineValue(realField) || record.get(realField);
            actualValue = record.get(realField);
          } else if (widgetType === 'RichText') {
            // 'RichText' 富文本特殊处理
            currentValue =
              record.getPristineValue(realField) || record.get(realField);
            actualValue = record.get(realField);
            // NOTE: 第一版本富文本富文本是Quill，数据是object类型的。 第二版富文本是ckeditor，存的是html字符串
            const isNullCurrentValue = getRichTextIsNull(
              JSON.stringify(currentValue),
            );
            const isNullActualValue = getRichTextIsNull(
              JSON.stringify(actualValue),
            );
            // 判断quill富文本是否为空
            if (isNullCurrentValue) {
              currentValue = '';
            }
            if (isNullActualValue) {
              actualValue = '';
            }
          } else if (MOMENT_FIELDS.includes(widgetType)) {
            // 'DateTime','Date','Time', // 时间相关
            const realFormat = DEFAULT_DATE_FORMAT[widgetType];
            currentValue =
              record?.get(realField) &&
              moment(record?.get(realField)).format(realFormat);
            actualValue = currentValue;
          } else if (
            MULTIPLE_FIELDS.includes(widgetType) &&
            Array.isArray(toJS(record.get(realField)))
          ) {
            // 多选值如果是数组，转为以逗号分割的字符串
            currentValue = toJS(record.get(realField)).join(',');
            actualValue = currentValue;
          } else if (widgetType === 'MasterDetail' && record.get(realField)) {
            // 判断多对一字段值为对象的情况
            if (typeof record.get(realField) === 'object') {
              currentValue = record.get(realField)?.id;
            }
            if (typeof record.get(realField) === 'string') {
              currentValue = record.get(realField);
            }
            actualValue = currentValue;
          } else {
            currentValue = record.get(realField);
            actualValue = currentValue;
          }
          if (fieldValueType === 'EXPRESSION') {
            // js表达式取值
            fieldValue = executeExpression(
              filterItem,
              record,
              funcConfig,
              realField,
            );
          }
          // 判断条件
          // // start switch
          switch (
            filter // 根据条件判断
          ) {
            case OPERATOR.IS: // 判断是否相等
              newFilterFlag = operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.IS_NOT: // 判断是否不相等
              newFilterFlag = !operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.NULL: // 判断为空
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.NOT_NULL: // 判断不为空
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IN_LIST: // 判断是否是数组
              newFilterFlag = fieldValue?.split(',')?.includes(currentValue);
              break;
            case OPERATOR.NOT_IN_LIST:
              newFilterFlag = !fieldValue?.split(',')?.includes(currentValue);
              break;
            case OPERATOR.IS_EMPTY:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_NOT_EMPTY:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IS_EARLY_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_LATER_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_EARLY_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_LATER_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.STARTS_WITH:
              newFilterFlag = compareText('start', fieldValue, currentValue);
              break;
            case OPERATOR.ENDS_WITH:
              newFilterFlag = compareText('end', fieldValue, currentValue);
              break;
            case OPERATOR.CONTAINS:
              newFilterFlag = compareText('contains', fieldValue, currentValue);
              break;
            case OPERATOR.DOES_NOT_CONTTAIN:
              newFilterFlag = !compareText(
                'contains',
                fieldValue,
                currentValue,
              );
              break;
            case OPERATOR.BETWEEN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_GREATER_THAN:
              newFilterFlag = Number(fieldValue) < Number(currentValue);
              break;
            case OPERATOR.IS_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) > Number(currentValue);
              break;
            case OPERATOR.IS_NOT_GREATER_THAN:
              newFilterFlag = Number(fieldValue) >= Number(currentValue);
              break;
            case OPERATOR.IS_NOT_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) <= Number(currentValue);
              break;
            case OPERATOR.IS_CURRENT_USER:
              newFilterFlag = currentValue === funcConfig.personId;
              break;
            case OPERATOR.EXISTS:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.DOES_NOT_EXISTS:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_LIKE:
              newFilterFlag = ['true', true].includes(actualValue);
              break;
            case OPERATOR.IS_STEP:
              newFilterFlag = ['false', false].includes(actualValue);
              break;
            case OPERATOR.IS_NOTHING:
              newFilterFlag = [undefined, '', null].includes(actualValue);
              break;
            default:
              break;
          }
          // // end switch
          // switch 运算后 => newFilterFlag
          // // start filters[i].conditio
          if (RELATION.AND === filterCondition) {
            // 如果 filters[i].condition === RELATION.AND
            filterFlag = filterFlag && newFilterFlag;
          } else {
            // 如果 filters[i].condition === RELATION.OR
            filterFlag = filterFlag || newFilterFlag;
          }
          // // end
          // 这里return无意义，后改为forEach，可优化掉
          return filterItem;
        });
      }
      if (RELATION.AND === condition) {
        conditionFlag = conditionFlag && filterFlag;
      } else {
        // 当只有一组条件是，且条件是或，已这组条件计算结果为准
        if (conditions?.length === 1) {
          conditionFlag = false;
        }
        conditionFlag = conditionFlag || filterFlag;
      }
      return conditionItem;
    });
  }
  return conditionFlag;
}

export function calculateConditions(
  parentKey,
  conditionMap,
  conditionIds,
  record,
  extraConditions,
  funcConfig,
) {
  let conditionFlag = false;
  if (conditionMap && conditionIds && record) {
    conditionIds.map((conditionId) => {
      if (!conditionFlag && conditionMap[conditionId]) {
        conditionFlag = calculateCondition(
          parentKey,
          conditionMap[conditionId],
          record,
          funcConfig,
        );
      }
      return conditionId;
    });
  }
  // 增加额外UI规则判断 (修改 extraConditions 判断， extraConditions为ObservableArray 影响判断，为空的时候也进来了）
  if (record && extraConditions && extraConditions.length > 0) {
    conditionFlag = calculateCondition(
      parentKey,
      extraConditions,
      record,
      funcConfig,
    );
  }
  return conditionFlag;
}
