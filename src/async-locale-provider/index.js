import React, { useEffect, useState } from 'react';
import { IntlProvider } from 'react-intl';
import esModule from '../async-router/esModule';

export default function asyncLocaleProvider(locale, getMessage, getLocaleData) {
  const AsyncLocaleProvider = (props) => {
    const [data, setData] = useState({ messages: null, localeData: null });

    useEffect(() => {
      const streams = [];
      streams.push(getMessage && getMessage().then(esModule));
      if (getLocaleData) {
        streams.push(getLocaleData().then(esModule));
      }
      Promise.all(streams).then(([messages]) => {
        setData({ messages });
      });
    }, [locale, getMessage, getLocaleData]);

    const { messages } = data;

    return messages ? (
      <IntlProvider
        {...props}
        locale={locale.replace('_', '-')}
        messages={messages}
      />
    ) : null;
  };
  AsyncLocaleProvider.displayName = 'AsyncLocaleProvider';

  return AsyncLocaleProvider;
}
