---
title: accessToken
nav:
  title: utils
---

# accessToken

## getAccessToken

获取当前token。

```tsx
/**
 * title: 基本使用
 */
import {getAccessToken} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>getAccessToken()</div>
        <br />
    	<div style={{color:'#BCBCBC'}}>结果：{getAccessToken()}</div>
    </div>)
}
```

## setAccessToken

设置token

| 入参      | 介绍                    | 类型   |
| --------- | ----------------------- | ------ |
| token     | token                   | string |
| tokenType | tokenType（例如bearer） | string |
| expiresIn | 失效时间（秒）          | number |
```jsx
/**
 * title: 基本使用
 */
import {setAccessToken} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>setAccessToken(token, tokenType, expiresIn)</div>
    </div>)
}
```

## getCookieToken

获取cookie

```jsx
/**
 * title: 基本使用
 */
import {getCookieToken} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>getCookieToken()</div>
        <br />
    	<div style={{color:'#BCBCBC'}}>结果：{getCookieToken()}</div>
    </div>)
}
```

## removeAccessToken

移除设置的token

```jsx
/**
 * title: 基本使用
 */
import {removeAccessToken} from '@zknow/utils';

export default ()=>{
    return (<div>
        <div>removeAccessToken()</div>
    </div>)
}
```

