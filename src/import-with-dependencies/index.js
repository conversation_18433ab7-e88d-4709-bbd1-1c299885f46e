import corsImport from '../cors-import';
import getChunkDependencies from '../external/getChunkDependencies';
import getChunkPath from '../external/getChunkPath';
import getInConcurrency from '../external/getInConcurrency';


export default (basePath, nameSpace, module) => {
  if (!window.entryManifest) return;
  if (!nameSpace) return;
  if (!window.entryManifest[nameSpace]) return;

  return getInConcurrency(
    getChunkDependencies(basePath, nameSpace, module),
    corsImport,
  ).then(() => corsImport(getChunkPath(basePath, nameSpace, module)));
};
