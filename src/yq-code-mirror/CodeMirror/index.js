import { Alert, List } from 'choerodon-ui';
import { Tabs } from 'choerodon-ui/pro';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/hint/show-hint.css';
import _ from 'lodash';
import React, { useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import ParamTree from './ParamTree';
import VariableTree from './VariableTree';
import './index.less';
// 代码高亮
import 'codemirror/addon/selection/active-line';
import 'codemirror/addon/display/autorefresh'
// 折叠代码
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldgutter.js';

require('codemirror/lib/codemirror.css');
require('codemirror/theme/material.css');
require('codemirror/theme/neat.css');
require('codemirror/mode/xml/xml.js');
require('codemirror/mode/javascript/javascript.js');

/**
 * 测试数据
 * const testvalue = `function findSequence(goal) {
    function find(start, history) {
      if (start == goal)
        return history;
      else if (start > goal)
        return null;
      else
        return find(start + 5, "(" + history + " + 5)") ||
               find(start * 3, "(" + history + " * 3)");
    }
    return find(1, "1");
  }`;
 */
const defaultNotice = `/*
** Script must return a value.
** example:
** var userName = $GetCurrentAttribute('iam_user_id:login_name')
** return userName;
*/\n`;

const actionDefaultNotice = `/*
** Script can set a value.
** example:
** $SetValue('login_name')
*/\n`;
const { TabPane } = Tabs;
const CodeMirrorRc = (props, ref) => {
  const {
    wrapperClassName,
    containerClassName,
    onChange,
    defaultValue,
    func,
    context,
    mode,
  } = props;
  const {
    intl,
    intlPrefix,
    prefixCls,
    type,
    tenantId,
    objectFieldDataSet,
    businessObjectId,
    variableTreeDs,
    defaultValueFlag,
  } = context;
  const [instance, setInstance] = useState(null);
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-nested-ternary
  const data = useRef(
    defaultValue === ''
      ? func === 'action'
        ? actionDefaultNotice
        : defaultNotice
      : defaultValue,
  );
  const handleOnChange = _.debounce(changeValue, 100);

  function changeValue(value) {
    onChange(value);
    data.current = value;
  }

  const handleCopyIntoEditor = (text) => {
    if (instance || defaultValue !== '' || defaultNotice) {
      if (instance) {
        instance.replaceSelection(text);
        data.current = instance.getValue();
        instance.focus();
      } else {
        data.current = `${data.current}${text}`;
      }
    } else {
      data.current = text;
    }
  };

  const getCodeArea = () => {
    return (
      <CodeMirror
        editorDidMount={(editor) => {
          setInstance(editor);
          setTimeout(() => {
            editor?.focus();
          }, 500);
        }}
        value={`${data.current}\n`}
        style={{ width: '100%', height: '4rem' }}
        options={{
          mode: { name: 'text/javascript' },
          autofocus: false, // 自动获取焦点
          autoRefresh: true, // 自动刷新
          styleActiveLine: true, // 光标代码高亮
          lineNumbers: true, // 显示行号
          smartIndent: true, // 自动缩进
          lineWrapping: true, // 自动换行
          foldGutter: true, // 代码折叠
          fixedGutter: false,
          gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'], // end
          matchBrackets: true, // 括号匹配，光标旁边的括号都高亮显示
          autoCloseBrackets: true, // 键入时将自动关闭()[]{}''""
          extraKeys: { Ctrl: 'autocomplete' },
          readOnly: false,
          lint: true,
        }}
        onChange={(editor, _data, value) => {
          if (!instance) {
            setInstance(editor);
          }
          handleOnChange(value);
        }}
      />
    );
  };

  function handleSelect(value) {
    handleCopyIntoEditor(value);
  }
  const functionRc = () => {
    return (
      <div
        className={`${prefixCls}-function-container`}
        style={{ height: '100%' }}
      >
        <Alert
          message={intl.formatMessage({
            id: 'zknow.components.function.params.tips',
            defaultMessage: '点击函数名称可以进行直接引用',
          })}
          type="info"
          showIcon
        />
        <div className={`${prefixCls}-function-content`}>
          <List>
            <List.Item
              onClick={() => {
                handleSelect("$GetValue('field')");
              }}
            >
              {intl.formatMessage({
                id: 'zknow.components.get.field.value',
                defaultMessage: '获取字段值',
              })}
            </List.Item>
            {defaultValueFlag && (
              <>
                <List.Item
                  onClick={() => {
                    handleSelect(
                      "$GetBusinessObject('businessCode', id, [field])",
                    );
                  }}
                >
                  {intl.formatMessage({
                    id: 'zknow.components.search.object',
                    defaultMessage: '根据Id查业务对象',
                  })}
                </List.Item>
                <List.Item
                  onClick={() => {
                    handleSelect(
                      "$SearchBusinessObject('businessCode', {field:value}, [field])",
                    );
                  }}
                >
                  {intl.formatMessage({
                    id: 'zknow.components.search.condition',
                    defaultMessage: '根据条件查业务对象',
                  })}
                </List.Item>
                <List.Item
                  onClick={() => {
                    handleSelect(
                      "$CallInterface('nameSpace', 'serverCode', 'interfaceCode', 'params')",
                    );
                  }}
                >
                  {intl.formatMessage({
                    id: 'zknow.components.fetch.interface',
                    defaultMessage: '调用接口',
                  })}
                </List.Item>
              </>
            )}
          </List>
        </div>
      </div>
    );
  };

  const getExpression = () => {
    return (
      <div className={`${prefixCls}-expression`}>
        <Tabs defaultActiveKey="field" style={{ marginTop: '10px' }}>
          <TabPane
            tab={intl.formatMessage({
              id: 'zknow.components.field',
              defaultMessage: '字段',
            })}
            key="field"
          >
            <ParamTree
              prefixCls={prefixCls}
              intlPrefix={intlPrefix}
              intl={intl}
              onCopyIntoEditor={handleCopyIntoEditor}
              treeParamsDataSet={objectFieldDataSet}
              businessObjectId={businessObjectId}
              tenantId={tenantId}
              isSite={type === 'site'}
            />
          </TabPane>
          <TabPane
            tab={intl.formatMessage({
              id: 'zknow.components.function',
              defaultMessage: '函数',
            })}
            key="function"
          >
            {functionRc()}
          </TabPane>
          <TabPane
            tab={intl.formatMessage({
              id: 'zknow.components.special.params',
              defaultMessage: '环境变量',
            })}
            key="variable"
          >
            <VariableTree
              defaultValueFlag={defaultValueFlag}
              variableTreeDs={variableTreeDs}
              prefixCls={prefixCls}
              intlPrefix={intlPrefix}
              intl={intl}
              onCopyIntoEditor={handleCopyIntoEditor}
              businessObjectId={businessObjectId}
              tenantId={tenantId}
              isSite={type === 'site'}
            />
          </TabPane>
        </Tabs>
      </div>
    );
  };

  useImperativeHandle(ref, () => ({
    getInstance: () => instance,
    setValue: (value) => {
      if (instance) {
        instance.setValue(value);
      }
    },
    getValue: () => {
      if (instance) {
        return instance.getValue();
      }
      return '';
    },
    setLoading,
  }), [instance]);

  return (
    <div className={`yqcloud-coding-container ${containerClassName || ''}`}>
      {loading ? (
        <div className="loading">
          <img src={`${window._env_.ICON_SERVER}/static/intelligent-loading-min.gif`} alt="loading"  />
        </div>
      ) : (
        <>
          <div
            className={`yqcloud-editor-wrapper ${wrapperClassName || ''} ${
              mode !== 'pure' ? '' : 'yqcloud-editor-wrapper-pure'
          }`}
        >
          {getCodeArea()}
          </div>
          {mode !== 'pure' && getExpression()}
        </>
      )}
    </div>
  );
};

const Forwarded = forwardRef(CodeMirrorRc);
export default Forwarded;
