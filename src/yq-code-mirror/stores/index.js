import { formatterCollections } from '@zknow/utils';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import ObjectFieldDataSet from './ObjectFieldDataSet';
import VariableTreeDataSet from './VaraiableDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = formatterCollections({
  code: ['zknow.components'],
})(
  injectIntl(
    inject('AppState', 'HeaderStore')(
      observer((props) => {
        const {
          intl,
          children,
          AppState: {
            currentMenuType: { organizationId: tenantId, type },
          },
          HeaderStore: {
            tenantConfig: { enableChatGptFlag },
          },
          defaultValueFlag = true, // true: 后端执行；false: 前端执行
        } = props;

        const prefixCls = 'yqcloud-codemirror';
        const intlPrefix = 'yqcloud.codemirror';
        const objectFieldDataSet = useMemo(
          () => new DataSet(ObjectFieldDataSet({ tenantId, type, intl })),
          [type],
        );
        const variableTreeDs = useMemo(
          () => new DataSet(VariableTreeDataSet({ intl, defaultValueFlag })),
        );

        const value = {
          ...props,
          intl,
          prefixCls,
          intlPrefix,
          tenantId,
          type,
          objectFieldDataSet,
          variableTreeDs,
          defaultValueFlag,
          enableChatGptFlag,
        };

        return <Store.Provider value={value}>{children}</Store.Provider>;
      }),
    ),
  ),
);
