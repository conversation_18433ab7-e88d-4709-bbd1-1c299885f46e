/* eslint-disable react/jsx-key */
/**
 * mode:button text other
 */
/**
  // other example
  const yqRef = useRef();
  function handleCoding() {
    yqRef?.current?.handleCoding();
  }
  <YqCodeMirror onChange={handleCodingValue} yqRef={yqRef} businessObjectId={detailDataSet?.current?.get('id')} defaultValue={record?.get('widgetConfig.expression')} />
  function handleCodingValue(value) {
    // 修改变量为js表达式
    record.set('widgetConfig.expression', value);
    record.set('widgetConfig.fieldAction', {
      actionType: 'expression',
      fieldId: record.get('code'),
      fieldValue: value,
    });
  }
  */
import { Tooltip } from 'choerodon-ui';
import { Modal, TextField, Button, message } from 'choerodon-ui/pro';
import classnames from 'classnames';
import { runInAction } from 'mobx';
import { observer } from 'mobx-react-lite';
import React, {
  useCallback,
  useContext,
  useImperativeHandle,
  useRef,
} from 'react';
import axios from 'axios';
import Icon from '../icon';
import CodeMirror from './CodeMirror';
import './index.less';
import store from './stores';

const YqCodeMirror = () => {
  const context = useContext(store);
  const {
    intlPrefix,
    yqRef,
    objectFieldDataSet,
    businessObjectId,
    defaultValue = '',
    onChange,
    intl,
    mode,
    record,
    name,
    disabled,
    viewId,
    variableFlag,
    itemId,
    func,
    readOnly,
    tenantId,
    businessObjectCode,
    enableChatGptFlag,
  } = context;
  const codeValueRef = useRef(null);
  const YqCodeMirrorRef = useRef();
  const codeMirrorRef = useRef();

  function handleOnChange(value) {
    if (onChange || record) {
      codeValueRef.current = value;
    } else {
      // eslint-disable-next-line no-console
      console.warn('there is no change function');
    }
  }
  const openOnlyCodeModal = () => {
    Modal.open({
      title: intl.formatMessage({
        id: 'zknow.components.script.setting',
        defaultMessage: '脚本设置',
      }),
      className: 'yqcloud-codeing-modal',
      children: (
        <CodeMirror
          ref={codeMirrorRef}
          onChange={(value) => handleOnChange(value)}
          defaultValue={record?.get(name) || defaultValue}
          mode={mode}
          context={context}
        />
      ),
      key: Modal.key(),
      destroyOnClose: true,
      style: { width: '8rem' },
      onOk: () => {
        if (onChange) {
          onChange(codeValueRef.current || defaultValue);
        }
      },
      okProps: { disabled: readOnly },
    });
  };

  const handleAi = async () => {
    codeMirrorRef.current?.setLoading?.(true); // 开始 loading
    try {
      const currentValue = codeMirrorRef.current?.getValue();
      if (currentValue) {
        const res = await axios.post(`/intelligent/v1/${tenantId}/chat_gpt/get-script?businessObjectId=${businessObjectId}`, { script: currentValue });
        if (!res?.failed) {
          setTimeout(() => {
            const currentValue = codeMirrorRef.current?.getValue();
            codeMirrorRef.current?.setValue(`${currentValue}\n${res.content?.result}`);
          }, 1000);
        }
      }
    } finally {
      codeMirrorRef.current?.setLoading?.(false); // 结束 loading
    }
  }

  const openCodeModal = useCallback(() => {
    Modal.open({
      title: intl.formatMessage({
        id: 'zknow.components.script.setting',
        defaultMessage: '脚本设置',
      }),
      className: 'yqcloud-codeing-modal',
      children: (
        <CodeMirror
          ref={codeMirrorRef}
          onChange={(value) => handleOnChange(value)}
          defaultValue={record?.get(name) || defaultValue}
          func={func}
          context={context}
        />
      ),
      key: Modal.key(),
      destroyOnClose: true,
      style: { width: '10rem' },
      onOk: () => {
        if (onChange || record) {
          if (mode === 'button' && record) {
            const expression =
              codeValueRef.current ?? record?.get(name) ?? defaultValue;
            record.set(name, expression);
          } else if (mode === 'text' && record) {
            // 这里真的离大谱（这段逻辑应该放到lowcode服务里）
            if (name === 'widgetConfig.expression') {
              record.set(
                'widgetConfig.expression',
                codeValueRef.current || record?.get(name) || defaultValue,
              );
              record.set('widgetConfig.fieldAction', {
                actionType: 'expression',
                fieldId: record.get('id')
                  ? record.get('code')
                  : `t_${record.get('code')}`,
                fieldValue:
                  codeValueRef.current || record?.get(name) || defaultValue,
              });
            } else {
              record.set(
                name,
                codeValueRef.current || record?.get(name) || defaultValue,
              );
            }
          } else {
            onChange(codeValueRef.current ?? defaultValue);
          }
        }
      },
      okProps: { disabled: readOnly },
      footer: (okBtn, cancelBtn) => (
        <div>
          <Button className={classnames('ai-button')} id='yq-codemirror-ai-button' onClick={handleAi} hidden={!enableChatGptFlag}>识别</Button>
          {okBtn}
          {cancelBtn}
        </div>
      ),
    });
  }, [defaultValue, record, record?.get(name), readOnly]);

  function handleConfig() {
    if ((businessObjectId || viewId) && !disabled) {
      runInAction(() => {
        // 等待配置信息执行
        if (variableFlag && viewId) {
          objectFieldDataSet.setQueryParameter('variableFlag', variableFlag);
          objectFieldDataSet.setQueryParameter('itemId', itemId);
          objectFieldDataSet.setQueryParameter('viewId', viewId);
          objectFieldDataSet.query();
        } else if (businessObjectId) {
          objectFieldDataSet.setQueryParameter(
            'businessObjectId',
            businessObjectId,
          );
          objectFieldDataSet.query();
        }
      });

      return new Promise((resolve, reject) => {
        setTimeout(() => {
          openCodeModal();
          resolve();
        }, 500);
      });
    }
    if (mode === 'pure' && !disabled) {
      openOnlyCodeModal();
    }
  }

  useImperativeHandle(yqRef, () => ({
    handleCoding: () => {
      handleConfig();
    },
    getCodeMirrorInstance: () => codeMirrorRef.current?.getInstance?.(),
  }));

  const notice = intl.formatMessage({
    id: 'zknow.components.expression.default.notice',
    defaultMessage: 'JavaScript 点击修改',
  });
  const tips = intl.formatMessage({
    id: 'zknow.components.expression.btn.tips',
    defaultMessage: '请选择业务对象后再配置脚本',
  });
  const renderWrapper = () => {
    let elements = [];
    if (mode === 'button') {
      elements = [
        <span
          className={classnames('expression-btn', { disabled })}
          type="button"
          ref={(node) => {
            YqCodeMirrorRef.current = node;
          }}
        >
          <Icon
            type="icon-hanshufunction"
            className={classnames('lc-hanshu-icon', {
              disabled,
            })}
            onClick={() => handleConfig()}
          />
        </span>,
      ];
    } else if (mode === 'text') {
      elements = [
        <span name={name} className="lc-js-expression">
          <TextField name={name} disabled renderer={() => notice} />
          <Tooltip
            placement="top"
            title={businessObjectId || viewId ? '' : tips}
          >
            <Icon
              type="icon-hanshufunction"
              className={classnames('lc-hanshu-icon', {
                disabled,
              })}
              onClick={() => handleConfig()}
            />
          </Tooltip>
        </span>,
      ];
    } else if (mode === 'pure') {
      elements = [
        <span name={name} className="lc-js-expression">
          <TextField name={name} disabled renderer={() => notice} />
          <Tooltip
            placement="top"
            title={businessObjectId || viewId ? '' : tips}
          >
            <Icon
              type="icon-hanshufunction"
              className={classnames('lc-hanshu-icon', {
                disabled,
              })}
              onClick={() => handleConfig()}
            />
          </Tooltip>
        </span>,
      ];
    } else {
      elements = [
        <div
          type="button"
          style={{ display: 'none' }}
          ref={(node) => {
            YqCodeMirrorRef.current = node;
          }}
        />,
      ];
    }
    return elements;
  };
  return renderWrapper();
};

export default observer(YqCodeMirror);
