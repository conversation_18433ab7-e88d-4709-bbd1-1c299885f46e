import * as dd from 'dingtalk-jsapi';
import queryString from 'query-string';
import { message } from 'choerodon-ui/pro';
import { AUTH_URL } from '../constants';
import axios from '../axios';
import getEnv from '../get-env';

function authorizeWeb() {
  const redirectPath = window.location.hash.slice(1).includes('unauthorized') ? localStorage.getItem('historyPath') || '/' : window.location.hash.slice(1);
  // 为了把这个hash传到oauth里要把#换成%23
  const uri = encodeURIComponent(`${window.location.origin}/#${redirectPath.includes('access_token') ? '/' : redirectPath}`);
  window.localStorage.removeItem('lastClosedId');
  // 这里是为了告诉oauth我要重定向的uri是什么，必须和client中对应，跳转到非client的页面会报错。
  if (window.location.href.indexOf('#') === -1) {
    window.location = `${AUTH_URL}&redirect_uri=${uri}`;
  } else if (window.location.href.indexOf('?') === -1) {
    window.location = `${AUTH_URL}&redirect_uri=${uri}%3FredirectFlag`;
  } else {
    window.location = `${AUTH_URL}&redirect_uri=${uri}%26redirectFlag`;
  }
}

export function authorizeMobile() {
  const searchParams = queryString.parse(window.location.search);
  // 如果是自动认证。不是从输入域名进的认证接口，就会有failed_redirect_uri，表示认证失败后需要重定向到的地址
  const failed_redirect_uri = searchParams?.failed_redirect_uri;
  const wechat_bind_code = searchParams?.wechat_bind_code;
  const failedParams = failed_redirect_uri ? `&failed_redirect_uri=${failed_redirect_uri}` : '';
  const wechatBindCodeParams = wechat_bind_code? `&wechat_bind_code=${wechat_bind_code}` : '';
  const redirectPath = window.location.hash.slice(1).includes('unauthorized') ? localStorage.getItem('historyPath') || '/' : window.location.hash.slice(1);
  // 为了把这个hash传到oauth里要把#换成%23
  const uri = encodeURIComponent(`${window.location.origin}/#${redirectPath.includes('access_token') ? '/' : redirectPath}`);
  window.localStorage.removeItem('lastClosedId');
  // 这里是为了告诉oauth我要重定向的uri是什么，必须和client中对应，跳转到非client的页面会报错。
  if (window.location.href.indexOf('#') === -1) {
    window.location = `${AUTH_URL}&redirect_uri=${uri}${failedParams}${wechatBindCodeParams}`;
  } else if (window.location.href.indexOf('?') === -1) {
    window.location = `${AUTH_URL}&redirect_uri=${uri}${failedParams}${wechatBindCodeParams}%3FredirectFlag`;
  } else {
    window.location = `${AUTH_URL}&redirect_uri=${uri}${failedParams}${wechatBindCodeParams}%26redirectFlag`;
  }
}

async function authorizeWxwork(mobileAppToken) {
  const uri = encodeURIComponent(`${window.location.origin}/#/h5/index`);
  try {
    const corpId = await axios.get(`${getEnv('API_HOST')}/oauth/public/workWeChatCorpId?mat=${mobileAppToken}`);
    if (corpId.failed) throw new Error(corpId.message);
    else {
      window.location = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${uri}&response_type=code&scope=snsapi_base&state=wxwork#wechat_redirect`;
    }
  } catch (e) {
    message.error(e.message);
  }
}

async function authorizeFeishu(mobileAppToken) {
  const uri = encodeURIComponent(`${window.location.origin}?type=feishuH5`);
  try {
    const appId = await axios.get(`${getEnv('API_HOST')}/oauth/public/feishuAppId?mat=${mobileAppToken}`);
    if (appId.failed) throw new Error(appId.message);
    else {
      window.location = `https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${uri}&app_id=${appId}&state=feishu`;
    }
  } catch (e) {
    message.error(e.message);
  }
}

async function authorizeDingTalk(mobileAppToken) {
  try {
    const corpId = await axios.get(`${getEnv('API_HOST')}/oauth/public/dingTalkCorpId?mat=${mobileAppToken}`);
    if (corpId.failed) throw new Error(corpId.message);
    else {
      dd.ready(function() {
        dd.runtime.permission.requestAuthCode({
            corpId, // 企业id
            onSuccess (info) {
              window.location = `${window.location.origin}/?code=${info.code}#/h5/index`;
            },
            onFail (err) {
              message.error(err);
            }
          });
      });
    }
  } catch (e) {
    message.error(e.message);
  }
}

export default function authorizeC7n(mobileAppToken) {
  const searchParams = queryString.parse(window.location.search);
  if (searchParams?.mobileRedirectUrl) {
    authorizeMobile();
  } else {
    authorizeWeb();
  }
  // const ua = window.navigator.userAgent.toLowerCase();
  // if (ua.includes('wxwork')) {
  //   authorizeWxwork(mobileAppToken);
  // } else if (dd.version) {
  //   authorizeDingTalk(mobileAppToken);
  // } else if (ua.includes('Lark') || ua.includes('lark')) {
  //   authorizeFeishu(mobileAppToken);
  // } else {
  //   authorizeWeb();
  // }

}

