import { useEffect } from 'react';
import changeThemeColor from '../change-theme-color';
import getEnv from '../get-env';
import useDynamicScript from '../use-dynamic-script';

const cache = new Set();

export default function useManifest(props) {
  const { scope } = props;
  const routeMap = getEnv('routeMap') || {};
  const manifestMap = getEnv('manifestMap') || {};
  const envRoute = manifestMap[scope] || getEnv(scope);
  const STATIC_URL = getEnv('STATIC_URL');
  const urlPrefix = envRoute || `${STATIC_URL}/${routeMap[scope] || scope}`;
  const { url = `${urlPrefix}/importManifest.js` } = props;

  const script = useDynamicScript({
    url,
  });
  const { ready, failed } = script;

  useEffect(() => {
    if (ready && !failed && !cache.has(urlPrefix)) {
      cache.add(urlPrefix);
      changeThemeColor({
        changeUrl: (uri) => `${urlPrefix}/${uri}`,
      });
    }
  }, [urlPrefix, ready, failed]);

  return script;
}
