import { defineConfig } from 'father';

export default defineConfig({
  // more father config: https://github.com/umijs/father/blob/master/docs/config.md
  // esm: { output: 'dist' },
  platform: 'browser',
  cjs: {
    output: 'lib',
    transformer: 'babel',
  },
  extraBabelPlugins: [
    [
      require.resolve('babel-plugin-import'),
      {
        libraryName: 'choerodon-ui',
        libraryDirectory: 'lib',
        style: false,
      },
      'c7n',
    ],
    [
      require.resolve('babel-plugin-import'),
      {
        libraryName: 'choerodon-ui/pro',
        libraryDirectory: 'lib',
        style: false,
      },
      'c7n-pro',
    ],
  ],
});
