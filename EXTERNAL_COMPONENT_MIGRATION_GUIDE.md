# ExternalComponent MF 2.0 升级迁移指南

## 🎯 升级概述

我已经为你的 `ExternalComponent` 组件升级了 Module Federation 2.0 支持，提供了两种使用方式：
1. **向后兼容模式** - 在原有组件中添加 MF 2.0 支持
2. **MF 2.0 专用版本** - 全新的 `ExternalComponentV2` 组件

## 📋 升级内容

### ✅ 已完成的升级：

1. **原有 ExternalComponent 增强** (`src/external-component/index.js`)
   - ✅ 添加 `useMF2` 参数启用 MF 2.0
   - ✅ 增强错误处理和重试机制
   - ✅ 保持完全向后兼容

2. **新增 ExternalComponentV2** (`src/external-component-v2/index.js`)
   - ✅ 专为 MF 2.0 设计
   - ✅ 完整的缓存机制
   - ✅ 服务可用性检查
   - ✅ 预加载功能

3. **迁移示例** (`examples/ExternalComponentMigration.jsx`)
   - ✅ 详细的使用示例
   - ✅ 迁移对比
   - ✅ 最佳实践

## 🚀 使用方式

### 方式一：原有组件升级（推荐渐进式迁移）

#### 保持原有用法（无需修改）：
```jsx
import ExternalComponent from '@zknow/components/external-component';

<ExternalComponent
  system={{
    scope: 'lcr',
    module: 'PageLoader',
  }}
  fallback={<div>加载中...</div>}
  setLoad={setLoading}
  viewId="example-view"
  mode="READONLY"
/>
```

#### 启用 MF 2.0 增强功能：
```jsx
<ExternalComponent
  system={{
    scope: 'lcr',
    module: 'PageLoader',
  }}
  useMF2={true}                    // 🆕 启用 MF 2.0
  fallback={<div>MF 2.0 加载中...</div>}
  setLoad={setLoading}
  onLoad={(component) => console.log('加载成功:', component)}
  onError={(error) => console.error('加载失败:', error)}
  retryCount={3}                   // 🆕 自动重试
  timeout={15000}                  // 🆕 超时控制
  viewId="example-view"
  mode="READONLY"
/>
```

### 方式二：使用 MF 2.0 专用版本（推荐新项目）

```jsx
import ExternalComponentV2 from '@zknow/components/external-component-v2';

<ExternalComponentV2
  remoteName="lcr"                 // 🆕 直接指定远程应用名
  modulePath="./PageLoader"        // 🆕 直接指定模块路径
  fallback={<div>MF 2.0 专用加载中...</div>}
  enableCache={true}               // 🆕 启用缓存
  checkAvailability={true}         // 🆕 检查服务可用性
  retryCount={3}                   // 🆕 重试机制
  timeout={20000}                  // 🆕 超时控制
  onLoad={(component) => console.log('加载成功:', component)}
  onError={(error) => console.error('加载失败:', error)}
  // 传递给组件的 props
  viewId="example-view"
  mode="READONLY"
/>
```

## 🔧 新增功能特性

### 1. 智能缓存机制
```jsx
// 启用缓存（默认开启）
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  enableCache={true}
/>

// 清理缓存
ExternalComponentV2.clearCache();
```

### 2. 服务可用性检查
```jsx
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  checkAvailability={true}         // 先检查服务是否可用
  notFound={<div>服务暂时不可用</div>}
/>
```

### 3. 组件预加载
```jsx
// 预加载组件
const preloadComponent = async () => {
  try {
    const Component = await ExternalComponentV2.preload('lcr', './PageLoader');
    console.log('预加载成功:', Component);
  } catch (error) {
    console.error('预加载失败:', error);
  }
};
```

### 4. 增强错误处理
```jsx
const CustomErrorComponent = ({ error, retry }) => (
  <div style={{ padding: '20px', border: '2px solid #ff4d4f' }}>
    <h4>组件加载失败</h4>
    <p>错误: {error.message}</p>
    <button onClick={retry}>重新加载</button>
  </div>
);

<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  ErrorComponent={CustomErrorComponent}
  retryCount={3}
  onError={(error) => {
    // 发送错误日志到监控系统
    console.error('组件加载错误:', error);
  }}
/>
```

### 5. 性能监控
```jsx
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  onLoad={(component) => {
    // 记录加载时间
    const loadTime = performance.now();
    console.log('组件加载耗时:', loadTime);
  }}
/>
```

## 📊 API 对比

### 原有 ExternalComponent

| 属性 | 类型 | 说明 |
|------|------|------|
| `system` | `{scope, module}` | 组件配置 |
| `fallback` | `ReactNode` | 加载中显示 |
| `notFound` | `ReactNode` | 未找到显示 |
| `ErrorComponent` | `Component` | 错误组件 |
| `setLoad` | `Function` | 加载状态回调 |

### 新增属性（原组件 + MF 2.0）

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `useMF2` | `boolean` | `false` | 启用 MF 2.0 |
| `onError` | `Function` | - | 错误回调 |
| `onLoad` | `Function` | - | 加载成功回调 |
| `retryCount` | `number` | `0` | 重试次数 |
| `timeout` | `number` | `30000` | 超时时间 |

### ExternalComponentV2 专用属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `remoteName` | `string` | - | 远程应用名 |
| `modulePath` | `string` | `'./'` | 模块路径 |
| `enableCache` | `boolean` | `true` | 启用缓存 |
| `checkAvailability` | `boolean` | `false` | 检查可用性 |
| `retryCount` | `number` | `2` | 重试次数 |
| `timeout` | `number` | `30000` | 超时时间 |

## 🔄 迁移策略

### 阶段一：保持现状（立即可用）
- 无需修改任何代码
- 原有功能完全保持

### 阶段二：渐进式升级（推荐）
- 在关键组件上添加 `useMF2={true}`
- 逐步添加错误处理和重试机制
- 监控性能和稳定性

### 阶段三：全面升级（可选）
- 新项目使用 `ExternalComponentV2`
- 老项目逐步迁移到新版本
- 享受完整的 MF 2.0 特性

## 🎯 最佳实践

### 1. 错误处理
```jsx
// 总是提供错误处理
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  ErrorComponent={CustomErrorComponent}
  onError={(error) => {
    // 发送错误日志
    logError('ExternalComponent', error);
  }}
/>
```

### 2. 性能优化
```jsx
// 启用缓存和预加载
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  enableCache={true}
  checkAvailability={true}
/>

// 预加载关键组件
useEffect(() => {
  ExternalComponentV2.preload('lcr', './PageLoader');
}, []);
```

### 3. 监控和调试
```jsx
// 添加性能监控
<ExternalComponentV2
  remoteName="lcr"
  modulePath="./PageLoader"
  onLoad={(component) => {
    // 记录加载成功
    analytics.track('component_loaded', {
      remoteName: 'lcr',
      modulePath: './PageLoader',
      loadTime: performance.now(),
    });
  }}
  onError={(error) => {
    // 记录加载失败
    analytics.track('component_load_failed', {
      remoteName: 'lcr',
      modulePath: './PageLoader',
      error: error.message,
    });
  }}
/>
```

## 🚀 立即开始

1. **无需修改** - 你的现有代码继续正常工作
2. **渐进升级** - 在需要的地方添加 `useMF2={true}`
3. **新项目** - 直接使用 `ExternalComponentV2`

查看 `examples/ExternalComponentMigration.jsx` 获取完整的使用示例！
