# Module Federation 2.0 共享组件渲染指南

## 🎯 概述

在 Module Federation 2.0 中，有两种主要的共享组件类型：
1. **远程组件** - 从其他微前端应用动态加载的组件
2. **本地共享组件** - 通过 shared 配置共享的依赖包中的组件

## 🚀 快速开始

### 1. 渲染远程组件

#### 基础用法：
```jsx
import React from 'react';
import { RemoteComponent } from './src/bin/common/utils/RemoteComponentRenderer';

function App() {
  return (
    <div>
      <h1>我的应用</h1>
      
      {/* 渲染远程按钮组件 */}
      <RemoteComponent 
        remoteName="shared-components"  // 远程应用名称
        modulePath="./Button"           // 组件路径
        type="primary"                  // 传递给组件的 props
        onClick={() => alert('点击了远程按钮')}
      >
        远程按钮
      </RemoteComponent>
      
      {/* 渲染远程表格组件 */}
      <RemoteComponent 
        remoteName="shared-components" 
        modulePath="./Table"
        dataSource={[
          { key: '1', name: '张三', age: 32 },
          { key: '2', name: '李四', age: 28 },
        ]}
        columns={[
          { title: '姓名', dataIndex: 'name' },
          { title: '年龄', dataIndex: 'age' },
        ]}
      />
    </div>
  );
}
```

#### 带加载状态和错误处理：
```jsx
<RemoteComponent 
  remoteName="shared-components" 
  modulePath="./ComplexComponent"
  
  // 加载状态
  fallback={<div>🔄 组件加载中...</div>}
  
  // 错误处理
  errorFallback={(error) => (
    <div style={{ color: 'red' }}>
      ❌ 组件加载失败: {error.message}
      <button onClick={() => window.location.reload()}>
        重新加载
      </button>
    </div>
  )}
  
  // 错误回调
  onError={(error) => {
    console.error('组件加载错误:', error);
    // 可以发送错误日志到监控系统
  }}
  
  // 加载成功回调
  onLoad={(Component) => {
    console.log('组件加载成功:', Component);
  }}
  
  // 传递给组件的 props
  title="远程组件标题"
  data={someData}
/>
```

### 2. 使用 React.lazy 方式

```jsx
import React, { Suspense } from 'react';
import { LazyRemoteComponent } from './src/bin/common/utils/RemoteComponentRenderer';

function App() {
  return (
    <div>
      <LazyRemoteComponent 
        remoteName="shared-components" 
        modulePath="./LazyCard"
        fallback={<div>懒加载中...</div>}
        title="懒加载卡片"
        content="这是一个懒加载的远程组件"
      />
    </div>
  );
}
```

### 3. 渲染本地共享组件

```jsx
import React from 'react';
// 这些组件通过 shared 配置共享，可以直接导入
import { Button, Input, Table } from '@zknow/components';
import { DatePicker, Modal } from 'choerodon-ui';
import { useRequest } from 'ahooks';

function MyComponent() {
  return (
    <div>
      {/* ZKnow 业务组件库 */}
      <Button type="primary" onClick={() => console.log('ZKnow 按钮')}>
        ZKnow 按钮
      </Button>
      
      <Input placeholder="ZKnow 输入框" />
      
      {/* Choerodon UI 组件库 */}
      <DatePicker placeholder="选择日期" />
      
      {/* 共享的工具库 */}
      <div>
        {useRequest(() => fetch('/api/data')).data}
      </div>
    </div>
  );
}
```

## 🔧 高级用法

### 1. 远程服务可用性检查

```jsx
import { RemoteComponentChecker } from './src/bin/common/utils/RemoteComponentRenderer';

<RemoteComponentChecker 
  remoteName="shared-components"
  fallback={<div>⚠️ 远程服务暂时不可用，请稍后重试</div>}
>
  <RemoteComponent 
    remoteName="shared-components" 
    modulePath="./ImportantComponent"
  />
</RemoteComponentChecker>
```

### 2. 批量加载远程组件

```jsx
import { RemoteComponentBundle } from './src/bin/common/utils/RemoteComponentRenderer';

function ComponentLibrary() {
  const [components, setComponents] = useState(null);

  const componentsToLoad = {
    Button: { remoteName: 'ui-lib', modulePath: './Button' },
    Input: { remoteName: 'ui-lib', modulePath: './Input' },
    Table: { remoteName: 'ui-lib', modulePath: './Table' },
    Modal: { remoteName: 'ui-lib', modulePath: './Modal' },
  };

  return (
    <div>
      <RemoteComponentBundle 
        components={componentsToLoad}
        fallback={<div>🔄 加载组件库中...</div>}
        onAllLoaded={(loadedComponents) => {
          console.log('组件库加载完成:', loadedComponents);
          setComponents(loadedComponents.components);
        }}
      />
      
      {components && (
        <div>
          <h3>可用组件:</h3>
          {Object.keys(components).map(name => (
            <div key={name}>{name} ✅</div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 3. 动态组件渲染

```jsx
import { loadRemoteModule } from './src/bin/common/utils/mfRuntime';

function DynamicComponentRenderer({ componentConfig }) {
  const [Component, setComponent] = useState(null);

  useEffect(() => {
    const loadComponent = async () => {
      try {
        const module = await loadRemoteModule(
          componentConfig.remoteName, 
          componentConfig.modulePath
        );
        setComponent(() => module.default || module);
      } catch (error) {
        console.error('动态加载组件失败:', error);
      }
    };

    loadComponent();
  }, [componentConfig]);

  if (!Component) {
    return <div>加载组件中...</div>;
  }

  return <Component {...componentConfig.props} />;
}

// 使用示例
const componentConfigs = [
  {
    remoteName: 'dashboard',
    modulePath: './Chart',
    props: { type: 'line', data: chartData }
  },
  {
    remoteName: 'forms',
    modulePath: './UserForm',
    props: { userId: 123 }
  }
];

function Dashboard() {
  return (
    <div>
      {componentConfigs.map((config, index) => (
        <DynamicComponentRenderer 
          key={index} 
          componentConfig={config} 
        />
      ))}
    </div>
  );
}
```

## 📋 最佳实践

### 1. 错误处理
- 始终提供 `fallback` 和 `errorFallback`
- 实现重试机制
- 记录错误日志

### 2. 性能优化
- 使用 `LazyRemoteComponent` 进行懒加载
- 预加载关键组件
- 实现组件缓存

### 3. 类型安全
- 为远程组件定义 TypeScript 接口
- 使用 props 验证
- 提供完整的类型定义

### 4. 监控和调试
- 监控组件加载性能
- 记录组件使用情况
- 提供开发模式调试信息

## 🔍 故障排除

### 常见问题：

1. **组件加载失败**
   - 检查远程服务是否可用
   - 验证 `remoteName` 和 `modulePath` 是否正确
   - 查看网络请求是否成功

2. **类型错误**
   - 确保远程组件导出正确
   - 检查 props 类型是否匹配

3. **样式问题**
   - 确保 CSS 正确加载
   - 检查样式隔离配置

## 📚 参考资料

- [Module Federation 官方文档](https://module-federation.io/)
- [React.lazy 文档](https://react.dev/reference/react/lazy)
- [错误边界文档](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)
