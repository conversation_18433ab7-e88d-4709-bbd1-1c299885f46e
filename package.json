{"name": "@zknow/utils", "version": "1.23.0", "description": "", "license": "MIT", "main": "lib/index.js", "files": ["lib"], "scripts": {"build": "father build", "build:watch": "father dev", "dev": "dumi dev", "docs:build": "dumi build", "doctor": "father doctor", "lint": "npm run lint:es && npm run lint:css", "lint:css": "stylelint \"{src,test}/**/*.{css,less}\"", "lint:es": "eslint \"{src,test}/**/*.{js,jsx,ts,tsx}\"", "prepare": "husky install && dumi setup", "start": "npm run dev"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{md,json}": ["prettier --write --no-error-on-unmatched-pattern"], "*.{css,less}": ["stylelint --fix", "prettier --write"], "*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{ts,tsx}": ["eslint --fix", "prettier --parser=typescript --write"]}, "dependencies": {"@cloudcare/browser-rum": "2.2.3", "classnames": "~2.2.6", "copy-to-clipboard": "^3.3.3", "dingtalk-jsapi": "^2.12.2", "loadjs": "^4.2.0", "universal-cookie": "^4.0.4", "url": "^0.11.0", "url-join": "^4.0.1", "webpack-theme-color-replacer": "^1.5.2"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@umijs/lint": "^4.0.0", "ahooks": "^3.7.2", "babel-plugin-import": "^1.13.8", "choerodon-ui": "*", "dumi": "^2.4.3", "eslint": "^8.23.0", "father": "4.4.5", "husky": "^8.0.2", "lint-staged": "^13.0.3", "mobx": "~4.15.7", "mobx-react": "~6.1.1", "mobx-react-lite": "^1.4.1", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "react": "^16.14.0", "react-dom": "^16.14.0", "react-intl": "^5.25.1", "stylelint": "^14.9.1", "warning": "^3.0.0", "webpack-theme-color-replacer": "^1.5.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "publishConfig": {"access": "public"}, "authors": []}