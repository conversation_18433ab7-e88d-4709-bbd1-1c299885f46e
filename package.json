{"name": "@zknow/boot", "version": "1.23.0", "description": "ZKnow Scaffold to run react application.", "routeIndex": "./tmpLib", "routeName": "boot", "license": "ISC", "main": "./lib/index.js", "bin": {"zknow-front-boot": "./bin/zknow-front-boot"}, "files": ["lib", "bin", ".default.env", "env.sh", "tsconfig.json"], "keywords": ["zknow", "boot", "framework", "frontend"], "author": "", "contributors": ["ZKNOW PLATFORM TEAM"], "scripts": {"compile": "gulp compile", "compile:bin": "node bin/zknow-front-boot compile --external -l tmpLib", "copy": "gulp copy", "dist:bin": "node --trace-deprecation bin/zknow-front-boot dist --config ./project.config.js -l tmpLib", "lint": "npm run lint:es && npm run lint:style", "lint-staged": "lint-staged", "lint-staged:es": "eslint src/**/*.{js,jsx} react/**/*.{js,jsx} ./project.config.js --ext '.js,.jsx'", "lint:es": "eslint src/**/*.{js,jsx} react ./project.config.js", "lint:style": "stylelint src/**/*.{less,css}", "pre-publish": "node bin/zknow-front-boot prepublish -p ./package.json", "precompile:bin": "npm run compile", "predist:bin": "npm run compile", "prestart": "npm run compile:bin", "prestart:bin": "npm run compile", "start": "gulp start", "start:bin": "node --trace-deprecation bin/zknow-front-boot start --config ./project.config.js -l tmpLib"}, "dependencies": {"@babel/core": "7.26.0", "@babel/generator": "7.26.0", "@babel/helper-remap-async-to-generator": "7.25.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.25.9", "@babel/plugin-proposal-do-expressions": "7.25.9", "@babel/plugin-proposal-export-default-from": "7.25.9", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-object-assign": "7.25.9", "@babel/plugin-transform-runtime": "7.26.10", "@babel/plugin-transform-typescript": "7.26.8", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.25.9", "@babel/runtime": "7.26.0", "@babel/traverse": "7.26.10", "@babel/types": "7.26.10", "@icon-park/react": "^1.3.1", "@module-federation/enhanced": "^0.15.0", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@umijs/babel-plugin-auto-css-modules": "^3.5.38", "add-asset-html-webpack-plugin": "^3.1.3", "ahooks": "3.7.2", "assert": "^2.0.0", "autoprefixer": "^9.7.3", "axios": ">=0.19.0 <= 0.19.2", "babel-core": "^7.0.0-0", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-loader": "^8.0.6", "babel-plugin-import": "^1.13.0", "babel-plugin-inline-import-data-uri": "^1.0.1", "babel-plugin-lodash": "^3.3.4", "babel-plugin-module-resolver": "^4.0.0", "babel-runtime": "^6.26.0", "buffer": "^5.6.0", "case-sensitive-paths-webpack-plugin": "^2.1.2", "chalk": "^2.4.2", "change-case": "^3.0.1", "classnames": "~2.2.6", "commander": "^2.11.0", "component-classes": "1.x", "copy-webpack-plugin": "^11.0.0", "cross-spawn": "^6.0.5", "crypto-browserify": "^3.12.0", "css-loader": "^3.2.0", "dimport": "^1.0.0", "dotenv": "^8.0.0", "dotenv-runtime-plugin": "^0.0.2", "eslint": "6.4.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.2.0", "eslint-module-utils": "2.4.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.17.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-no-chinese": "^1.2.256", "eslint-plugin-react": "^7.14.3", "eslint-plugin-react-hooks": "^2.4.0", "eslint-tinker": "^0.5.0", "file-loader": "^4.2.0", "friendly-errors-webpack-plugin": "^1.7.0", "fs-extra": "^8.1.0", "glob": "^7.1.4", "globby": "^9.2.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-style-aliases": "^1.1.11", "hash-sum": "^2.0.0", "history": "^4.6.3", "html-webpack-plugin": "^5.5.0", "husky": "^3.1.0", "jest": "^24.8.0", "less": "^3.10.3", "less-loader": "~4.1.0", "less-plugin-npm-import": "^2.1.0", "lib-flexible": "^0.3.2", "lint-staged": "^7.3.0", "lodash": "^4.17.5", "merge-stream": "^1.0.1", "merge2": "^1.2.4", "mini-css-extract-plugin": "^2.6.0", "mkdirp": "^0.5.1", "mobx": "~4.15.7", "mobx-react": "~6.1.1", "mobx-react-lite": "^1.4.1", "moment": "2.24.0", "nanoid": "^5.0.1", "nunjucks": "^3.2.0", "object.omit": "^3.0.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "os-browserify": "^0.3.0", "postcss": "^7.0.26", "postcss-flexbugs-fixes": "^4.2.1", "postcss-loader": "^3.0.0", "postcss-normalize": "^9.0.0", "postcss-preset-env": "^6.7.0", "pretty-quick": "^2.0.1", "prop-types": "^15.5.10", "px2rem-loader": "^0.1.9", "query-string": "^4.3.4", "react": "^16.14.0", "react-color": "^2.17.0", "react-dev-utils": "^5.0.2", "react-dom": "^16.14.0", "react-helmet": "^5.2.0", "react-hot-loader": "^3.1.1", "react-intl": "^5.25.1", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "resize-observer-polyfill": "^1.5.1", "resolve": "^1.1.7", "rimraf": "2.4.3", "run-sequence": "^1.2.2", "rxjs": "^5.5.12", "sanitize.css": "12.0.1", "sockjs-client": "^1.3.0", "stream-browserify": "^3.0.0", "style-loader": "0.13.1", "stylelint": "^10.1.0", "stylelint-config-prettier": "^5.3.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^18.3.0", "stylelint-declaration-block-no-ignored-properties": "^2.2.0", "stylelint-order": "^3.1.1", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^4.1.0", "through2": "^3.0.1", "timeago-react": "^3.0.0", "timers-browserify": "^2.0.11", "ts-loader": "^6.2.1", "tslib": "2.5.2", "typescript": "^4.8.4", "universal-cookie": "^4.0.4", "unzip": "^0.1.11", "url": "^0.11.0", "url-loader": "^2.3.0", "uuid": "^3.3.3", "warning": "^3.0.0", "webpack": "^5.0.0", "webpack-dev-server": "^4.9.0", "webpack-theme-color-replacer": "^1.5.2", "webpackbar": "^4.0.0", "workbox-webpack-plugin": "6.6.0", "worker-loader": "^3.0.2"}, "devDependencies": {"@types/babel-types": "^7.0.7", "cross-env": "^6.0.3", "pdfjs-dist": "^2.4.456", "react-pdf": "^5.0.0"}, "overrides": {"babel-plugin-lodash": {"@babel/types": "~7.20.0"}, "@babel/core": {"@babel/traverse": "~7.22.6"}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx}": ["npm run lint-staged:es"], "src/**/*.{scss,less}": "stylelint"}, "browserslist": ["last 2 versions", "Firefox ESR", "> 1%"]}