{"name": "@zknow/components", "version": "1.22.0", "description": "", "license": "MIT", "module": "lib/index.js", "files": ["lib"], "scripts": {"build": "father build", "build:watch": "father dev", "dev": "dumi dev", "docs:build": "dumi build", "doctor": "father doctor", "lint": "npm run lint:es && npm run lint:css", "lint:css": "stylelint \"{src,test}/**/*.{css,less}\"", "lint:es": "eslint \"{src,test}/**/*.{js,jsx,ts,tsx}\"", "start": "npm run dev"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{md,json}": ["prettier --write --no-error-on-unmatched-pattern"], "*.{css,less}": ["stylelint --fix", "prettier --write"], "*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{ts,tsx}": ["eslint --fix", "prettier --parser=typescript --write"]}, "resolutions": {"react": "^16.14.0", "react-dom": "^16.14.0", "react-router": "^5.0.0", "react-router-dom": "^5.0.0"}, "dependencies": {"@hanyk/rc-viewer": "0.0.3", "@zknow/utils": ">=1.22.0-develop.0 < 1.22.0-develop-1", "cropperjs": "^1.5.13", "qs": "^6.11.0", "react-colorful": "^5.6.1", "react-cropper": "^2.1.8", "react-draggable": "^4.4.5", "react-fast-compare": "^3.2.0", "react-router-dom": "^5.0.0", "reactcss": "^1.2.3", "screen-shot-hdpi": "^1.7.2", "simplebar-react": "^2.4.3", "substyle": "^9.4.1", "use-scroll-to-bottom": "^1.1.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@types/lodash": "^4.14.191", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-router-dom": "^5.3.3", "@umijs/babel-plugin-auto-css-modules": "^3.5.36", "@umijs/lint": "^4.0.0", "ahooks": "3.7.2", "axios": "0.19.2", "babel-plugin-import": "^1.13.8", "choerodon-ui": "*", "classnames": "^2.3.2", "dumi": "^2.4.3", "eslint": "^8.23.0", "father": "4.4.5", "husky": "^8.0.1", "less": "^3.10.3", "lint-staged": "^13.0.3", "lodash": "^4.17.21", "mobx": "~4.15.7", "mobx-react": "~6.1.1", "mobx-react-lite": "^1.4.1", "moment": "^2.29.4", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "prop-types": "^15.8.1", "query-string": "^7.1.1", "react": "^16.14.0", "react-dom": "^16.14.0", "react-intl": "^5.4.5", "stylelint": "^14.9.1", "timeago-react": "^3.0.5", "universal-cookie": "^4.0.4", "uuid": "^3.3.3"}, "peerDependencies": {"react": "^16.14.0", "react-dom": "^16.14.0", "react-router": "^5.0.0", "react-router-dom": "^5.0.0"}, "publishConfig": {"access": "public"}, "authors": []}