image: registry.aliyuncs.com/brapps/yqcloud-ci:0.9.1-240115

stages:
  - build
  - release
  - codecloc
  - clean
variables:
  ENV_PRO_CLIENT_ID: yqcloud
  GIT_SUBMODULE_STRATEGY: recursive

codecloc:
  stage: codecloc
  script:
    - if git log -1 --pretty=%B | grep -q 'Merge branch'; then exit 0; fi
    - export changed=`git diff --numstat HEAD^ HEAD | awk '{ added += $1; deleted += $2 } END { print  added, deleted }'`
    - export gitrev=`git rev-parse HEAD`
    - export gitmail=`git log -1 --pretty=format:'%ae'`
    - export gitusername=`git log -1 --pretty=format:'%an'`
    - cloc --vcs=git --json | jq '.SUM' > now.json
    - git checkout HEAD^
    - cloc --vcs=git --json | jq '.SUM' > before.json
    - |-
      data=`jq -s '{now: .[0], before: .[1]}' now.json before.json | jq ". + {\"added\": ${changed% *},\"deleted\": ${changed#* } }"`
    - |-
      curl \
      -H "GIT-USERNAME: ${gitusername}" \
      -H "GIT-MAIL: ${gitmail}" \
      -H "GIT-REV: ${gitrev}" \
      -H "GIT-REPO-NAME: ${GROUP_NAME}/${PROJECT_NAME}" \
      -d "${data}" ${ZK_CLOC_URL}

build:
  stage: build
  script:
    # 获取当前时间的YYYYMMDDHHmmss格式的时间戳
    - timestamp=$(date "+%Y%m%d%H%M%S")
    # 读取package.json文件并使用jq命令获取version字段的值
    - version=$(cat package.json | jq -r '.version')
    # 将version字段的值改为当前版本加当前分支加YYYYMMDDHHmmss格式的时间戳
    - sed -i "s/${version}/${version}-$CI_COMMIT_REF_NAME.${timestamp}/g" package.json
    - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}" > ~/.npmrc
    - |
      if [[ $OH_MY_NEXUS ]];then
        export ALIYUNXIAO_NPM=${OH_MY_NEXUS}
      fi
      if [[ -z $ALIYUNXIAO_NPM ]];then
        export ALIYUNXIAO_NPM=${NPM_REGISTRY}
      fi
    # - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}" >> ~/.npmrc
    - pnpm install --registry ${ALIYUNXIAO_NPM} --loglevel verbose
    - chmod -R 755 .
    # - chmod -R 755 node_modules
    - pnpm run build
    - echo "//${NPM_REGISTRY##http*://}:_authToken=${NPM_TOKEN}" >> ~/.npmrc
    - pnpm publish --registry ${NPM_REGISTRY}
  only:
    refs:
       - develop
       - /^release-.*$/

release:
  stage: release
  script:
    - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}"> ~/.npmrc
    - npm install --registry https://packages.aliyun.com/60b878982c5969c370c5e461/npm/npm-registry/ --loglevel verbose
    - chmod -R 755 .
    # - chmod -R 755 node_modules
    - npm run compile
    - echo "//${NPM_REGISTRY##http*://}:_authToken=${NPM_TOKEN}" >> ~/.npmrc
    - npm publish --registry ${NPM_REGISTRY}
  only:
    refs:
      - tags

clean:
  stage: clean
  script:
    - |
      if [[ $OH_MY_NEXUS ]];then
        export MODULE_NAME=`node -p "require('./package.json').name"`
        curl -L "${OH_MY_NEXUS}/clean/${MODULE_NAME}"
      fi

.auto_devops: &auto_devops |
  http_status_code=`curl -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"`
  if [ "$http_status_code" != "200" ]; then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh
  export_commit_tag || true

before_script:
  - *auto_devops
