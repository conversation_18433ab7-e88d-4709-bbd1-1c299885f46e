image: registry.aliyuncs.com/zknow/yqcloud-ci:20221128.16

stages:
  - build
  - release
  - clean

build:
  stage: build
  script:
    - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}" > ~/.npmrc
    - |
      if [[ $OH_MY_NEXUS ]];then
        export ALIYUNXIAO_NPM=${OH_MY_NEXUS}
      fi
      if [[ -z $ALIYUNXIAO_NPM ]];then
        export ALIYUNXIAO_NPM=${NPM_REGISTRY}
      fi
    # - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}" >> ~/.npmrc
    - pnpm install --registry ${ALIYUNXIAO_NPM} --loglevel verbose
    - chmod -R 755 .
    # - chmod -R 755 node_modules
    - pnpm run compile
    - pnpm run pre-publish
    - echo "//${NPM_REGISTRY##http*://}:_authToken=${NPM_TOKEN}" >> ~/.npmrc
    - pnpm publish --no-git-checks --registry ${NPM_REGISTRY}

release:
  stage: release
  script:
    - echo "//${ALIYUNXIAO_NPM##http*://}:_authToken=${ALIYUNXIAO_NPM_TOKEN}"> ~/.npmrc
    - npm install --registry https://packages.aliyun.com/60b878982c5969c370c5e461/npm/npm-registry/ --loglevel verbose
    - chmod -R 755 .
    # - chmod -R 755 node_modules
    - npm run compile
    - echo "//${NPM_REGISTRY##http*://}:_authToken=${NPM_TOKEN}" >> ~/.npmrc
    - npm publish --registry ${NPM_REGISTRY}
  only:
    refs:
      - tags

clean:
  stage: clean
  script:
    - |
      if [[ $OH_MY_NEXUS ]];then
        export MODULE_NAME=`node -p "require('./package.json').name"`
        curl -L "${OH_MY_NEXUS}/clean/${MODULE_NAME}"
      fi

.auto_devops: &auto_devops |
  http_status_code=`curl -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"`
  if [ "$http_status_code" != "200" ]; then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh
  export_commit_tag || true

before_script:
  - *auto_devops
