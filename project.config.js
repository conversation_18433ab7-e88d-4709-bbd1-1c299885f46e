const path = require('path');

const packageName = require(path.resolve('package.json')).name;

module.exports = {
  projectType: 'ZK<PERSON>OW',
  master: '@zknow/apps-master',
  modules: [
    '.',
  ],
  emailBlackList: 'qq',
  resourcesLevel: ['site', 'user'],
  webpackConfig(config) {
    config.resolve.alias = {
      ...config.resolve.alias,
      [packageName]: path.resolve('tmp/transfer.index.js'),
    };
    return config;
  },
  routeMap: {
    lcapp: 'lch',
  },
}
